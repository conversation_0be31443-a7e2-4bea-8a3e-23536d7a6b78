#[cfg(test)]
mod tests {
    use libwebsocket_rs::encoding::sbe::decoder::SbeDecoder;

    #[test]
    fn test_binance_sbe_detection() {
        // 您实际收到的数据前8字节
        let actual_header = [32, 0, 17, 39, 1, 0, 0, 0];

        println!("测试Binance SBE数据检测");
        println!("========================");

        // 解析头部信息
        let block_length = u16::from_le_bytes([actual_header[0], actual_header[1]]);
        let template_id = u16::from_le_bytes([actual_header[2], actual_header[3]]);
        let schema_id = u16::from_le_bytes([actual_header[4], actual_header[5]]);
        let version = u16::from_le_bytes([actual_header[6], actual_header[7]]);

        println!("解析的头部信息:");
        println!("  block_length: {}", block_length);
        println!("  template_id: {} (0x{:04X})", template_id, template_id);
        println!("  schema_id: {}", schema_id);
        println!("  version: {}", version);

        // 创建一个完整的测试消息（头部 + 足够的数据）
        let mut test_data = actual_header.to_vec();
        // 添加足够的数据以满足block_length要求
        test_data.extend_from_slice(&vec![0u8; block_length as usize]);

        println!("\n测试数据长度: {}", test_data.len());
        println!("前16字节: {:02X?}", &test_data[..16.min(test_data.len())]);

        // 测试SBE检测
        let is_sbe = SbeDecoder::is_sbe_message(&test_data);
        println!("\nSBE检测结果: {}", is_sbe);

        if is_sbe {
            println!("✓ 成功识别为SBE格式！");

            // 尝试解码
            if let Some(sbe_message) = SbeDecoder::decode(&test_data) {
                println!("✓ SBE消息解码成功: {:?}", sbe_message);
            } else {
                println!("✗ SBE消息解码失败");
            }
        } else {
            println!("✗ 未能识别为SBE格式");

            // 分析为什么检测失败
            println!("\n分析检测失败原因:");
            if test_data.len() < 8 {
                println!("  - 数据长度不足（需要至少8字节）");
            } else {
                println!("  - 数据长度足够: {} 字节", test_data.len());
            }

            if schema_id != 1 {
                println!("  - schema_id 不正确: {} (期望: 1)", schema_id);
            } else {
                println!("  - schema_id 正确: {}", schema_id);
            }

            if version != 0 && version != 1 {
                println!("  - version 不正确: {} (期望: 0 或 1)", version);
            } else {
                println!("  - version 正确: {}", version);
            }

            let expected_min_length = 8 + block_length as usize;
            if test_data.len() < expected_min_length {
                println!(
                    "  - 总长度不足: {} < {} (头部8字节 + block_length {}字节)",
                    test_data.len(),
                    expected_min_length,
                    block_length
                );
            } else {
                println!(
                    "  - 总长度足够: {} >= {}",
                    test_data.len(),
                    expected_min_length
                );
            }
        }
    }
}
