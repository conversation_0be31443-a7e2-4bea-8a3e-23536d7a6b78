extern crate env_logger;
extern crate libwebsocket_rs;
extern crate log;

use std::cell::RefCell;
use std::net::IpAddr;
use std::net::Ipv4Addr;
use std::net::SocketAddr;
use std::ops::Add;
use std::rc::Rc;
use std::str::FromStr;

use libwebsocket_rs::CallbackData;
use libwebsocket_rs::Message;
use libwebsocket_rs::Settings;
use libwebsocket_rs::WebSocket;
use libwebsocket_rs::WebSocketHandle;
use libwebsocket_rs::net::utils::url::Url;
use log::LevelFilter;
use mio::Token;

#[test]
fn test_connection() {
    let _ = env_logger::builder()
        .is_test(true)
        .filter_level(LevelFilter::Trace)
        .try_init();
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_secs(10));
    let count = Rc::new(RefCell::new(0));
    let count_clone = count.clone();
    const N: usize = 1024 * 32;
    let callback = move |_handle: &mut WebSocketHandle<N>, data: CallbackData| {
        match data {
            CallbackData::Message(_, response) => match response {
                Message::WebsocketPayload(response) => {
                    println!(
                        "Message::WebsocketPayload: {:?}",
                        String::from_utf8_lossy(response.as_ref())
                    );
                    let mut count = count_clone.borrow_mut();
                    *count = count.add(1);
                    println!("count: {:?}", count);
                    _handle.stop();
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(_) => {
                println!("ConnectionOpen");
            }
            _ => (),
        }
        Ok(())
    };
    let mut ws = WebSocket::new(settings, callback).unwrap();
    let mut url: Url = "wss://echo.websocket.org".into();
    url.socket_addr = Some(SocketAddr::new(
        IpAddr::V4(Ipv4Addr::from_str("**************").unwrap()),
        url.port,
    ));
    ws.connect(url, Token(0)).unwrap();
    ws.run().unwrap();
    println!("count: {:?}", count.borrow());
    assert!(count.borrow().eq(&1));
}
