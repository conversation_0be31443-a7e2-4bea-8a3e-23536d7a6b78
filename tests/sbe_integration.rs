use libwebsocket_rs::encoding::{
    market_data::{MarketDataFormat, MarketDataParser, MarketDataSubscription},
    sbe::{
        book_ticker::parse_sbe_bookticker,
        decoder::SbeDecoder,
        schema::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SbeMessageType},
    },
};

#[test]
fn test_sbe_header_creation_and_parsing() {
    let header = SbeHeader {
        block_length: 56,
        template_id: 1001,
        schema_id: 1,
        version: 1,
    };

    // 模拟头部字节序列化
    let mut header_bytes = Vec::new();
    header_bytes.extend_from_slice(&header.block_length.to_le_bytes());
    header_bytes.extend_from_slice(&header.template_id.to_le_bytes());
    header_bytes.extend_from_slice(&header.schema_id.to_le_bytes());
    header_bytes.extend_from_slice(&header.version.to_le_bytes());

    let parsed_header = SbeHeader::from_bytes(&header_bytes).unwrap();
    // 复制字段值以避免对packed字段的引用
    let block_length = parsed_header.block_length;
    let template_id = parsed_header.template_id;
    let schema_id = parsed_header.schema_id;
    let version = parsed_header.version;

    assert_eq!(block_length, 56);
    assert_eq!(template_id, 1001);
    assert_eq!(schema_id, 1);
    assert_eq!(version, 1);
    assert!(parsed_header.is_valid());
    assert_eq!(parsed_header.message_type(), SbeMessageType::BookTicker);
}

#[test]
fn test_sbe_bookticker_parsing() {
    // 使用新的Binance SBE格式创建测试数据
    let symbol = "BTCUSDT";
    let symbol_bytes = symbol.as_bytes();
    let total_length = 51 + symbol_bytes.len(); // 50字节固定字段 + 1字节长度 + symbol长度
    let mut data = vec![0u8; total_length];

    // Event time: 1640995200000000 (microseconds)
    let event_time = 1640995200000000i64;
    data[0..8].copy_from_slice(&event_time.to_le_bytes());

    // Book update ID: 12345
    let book_update_id = 12345i64;
    data[8..16].copy_from_slice(&book_update_id.to_le_bytes());

    // Price exponent: -4, Qty exponent: -8
    data[16] = (-4i8) as u8;
    data[17] = (-8i8) as u8;

    // Bid price: 50000.0 -> mantissa = 500000000
    let bid_price_mantissa = 500000000i64;
    data[18..26].copy_from_slice(&bid_price_mantissa.to_le_bytes());

    // Bid qty: 1.5 -> mantissa = 150000000
    let bid_qty_mantissa = 150000000i64;
    data[26..34].copy_from_slice(&bid_qty_mantissa.to_le_bytes());

    // Ask price: 50001.0 -> mantissa = 500010000
    let ask_price_mantissa = 500010000i64;
    data[34..42].copy_from_slice(&ask_price_mantissa.to_le_bytes());

    // Ask qty: 2.0 -> mantissa = 200000000
    let ask_qty_mantissa = 200000000i64;
    data[42..50].copy_from_slice(&ask_qty_mantissa.to_le_bytes());

    // Symbol length and data
    data[50] = symbol_bytes.len() as u8;
    data[51..51 + symbol_bytes.len()].copy_from_slice(symbol_bytes);

    let ticker = parse_sbe_bookticker(&data).unwrap();
    assert_eq!(ticker.symbol, "BTCUSDT");
    assert_eq!(ticker.bid_price, 50000.0);
    assert_eq!(ticker.bid_qty, 1.5);
    assert_eq!(ticker.ask_price, 50001.0);
    assert_eq!(ticker.ask_qty, 2.0);
    assert_eq!(ticker.event_time, 1640995200000000);
    assert_eq!(ticker.book_update_id, 12345);
}

#[test]
fn test_complete_sbe_message_decoding() {
    // 创建完整的SBE消息（头部 + 消息体）
    let mut complete_message = Vec::new();

    // SBE头部
    let header_bytes = [
        56, 0, // block_length = 56
        233, 3, // template_id = 1001 (BookTicker)
        1, 0, // schema_id = 1
        1, 0, // version = 1
    ];
    complete_message.extend_from_slice(&header_bytes);

    // 消息体
    let mut message_body = vec![0u8; 56];

    // Symbol: "ETHUSDT"
    let symbol_bytes = b"ETHUSDT\0\0\0\0\0\0\0\0\0";
    message_body[0..16].copy_from_slice(symbol_bytes);

    // Prices and quantities
    let bid_price = 3000.0f64.to_le_bytes();
    let bid_qty = 10.0f64.to_le_bytes();
    let ask_price = 3001.0f64.to_le_bytes();
    let ask_qty = 15.0f64.to_le_bytes();
    let timestamp = 1640995200000u64.to_le_bytes();

    message_body[16..24].copy_from_slice(&bid_price);
    message_body[24..32].copy_from_slice(&bid_qty);
    message_body[32..40].copy_from_slice(&ask_price);
    message_body[40..48].copy_from_slice(&ask_qty);
    message_body[48..56].copy_from_slice(&timestamp);

    complete_message.extend_from_slice(&message_body);

    // 使用SBE解码器解码
    let decoded_message = SbeDecoder::decode(&complete_message).unwrap();

    match decoded_message {
        libwebsocket_rs::encoding::sbe::decoder::SbeMessage::BookTicker(ticker) => {
            assert_eq!(ticker.symbol, "ETHUSDT");
            assert_eq!(ticker.bid_price, 3000.0);
            assert_eq!(ticker.bid_qty, 10.0);
            assert_eq!(ticker.ask_price, 3001.0);
            assert_eq!(ticker.ask_qty, 15.0);
        }
        _ => panic!("Expected BookTicker message"),
    }
}

#[test]
fn test_market_data_parser_sbe_format() {
    let parser = MarketDataParser::new(MarketDataFormat::Sbe);

    // 创建完整的SBE消息
    let mut complete_message = Vec::new();

    // SBE头部
    let header_bytes = [
        56, 0, // block_length = 56
        233, 3, // template_id = 1001
        1, 0, // schema_id = 1
        1, 0, // version = 1
    ];
    complete_message.extend_from_slice(&header_bytes);

    // 消息体
    let mut message_body = vec![0u8; 56];
    let symbol_bytes = b"ADAUSDT\0\0\0\0\0\0\0\0\0";
    message_body[0..16].copy_from_slice(symbol_bytes);

    let bid_price = 0.5f64.to_le_bytes();
    let bid_qty = 1000.0f64.to_le_bytes();
    let ask_price = 0.51f64.to_le_bytes();
    let ask_qty = 2000.0f64.to_le_bytes();
    let timestamp = 1640995200000u64.to_le_bytes();

    message_body[16..24].copy_from_slice(&bid_price);
    message_body[24..32].copy_from_slice(&bid_qty);
    message_body[32..40].copy_from_slice(&ask_price);
    message_body[40..48].copy_from_slice(&ask_qty);
    message_body[48..56].copy_from_slice(&timestamp);

    complete_message.extend_from_slice(&message_body);

    // 使用市场数据解析器解析
    let market_data = parser.parse(&complete_message).unwrap();
    let book_ticker = market_data.as_book_ticker().unwrap();

    assert_eq!(book_ticker.symbol, "ADAUSDT");
    assert_eq!(book_ticker.bid_price, 0.5);
    assert_eq!(book_ticker.bid_qty, 1000.0);
    assert_eq!(book_ticker.ask_price, 0.51);
    assert_eq!(book_ticker.ask_qty, 2000.0);
}

#[test]
fn test_market_data_parser_json_format() {
    let parser = MarketDataParser::new(MarketDataFormat::Json);
    let json_data = r#"{"s":"SOLUSDT","b":"100.50","B":"50.0","a":"100.60","A":"75.0"}"#;

    let market_data = parser.parse(json_data.as_bytes()).unwrap();
    let book_ticker = market_data.as_book_ticker().unwrap();

    assert_eq!(book_ticker.symbol, "SOLUSDT");
    assert_eq!(book_ticker.bid_price, 100.50);
    assert_eq!(book_ticker.ask_price, 100.60);
}

#[test]
fn test_auto_detect_format() {
    // 测试JSON格式自动检测
    let json_data = r#"{"s":"DOTUSDT","b":"25.50","B":"100.0","a":"25.60","A":"150.0"}"#;
    let market_data = MarketDataParser::parse_auto_detect(json_data.as_bytes()).unwrap();

    assert_eq!(market_data.symbol(), Some("DOTUSDT"));

    // 测试SBE格式自动检测
    let mut sbe_message = Vec::new();
    let header_bytes = [56, 0, 233, 3, 1, 0, 1, 0];
    sbe_message.extend_from_slice(&header_bytes);
    sbe_message.extend_from_slice(&vec![0u8; 56]);

    // 验证SBE消息被正确识别
    assert!(SbeDecoder::is_sbe_message(&sbe_message));
}

#[test]
fn test_subscription_configuration() {
    // 测试JSON订阅配置
    let json_sub = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Json);
    assert_eq!(json_sub.symbol, "BTCUSDT");
    assert_eq!(json_sub.format, MarketDataFormat::Json);
    assert_eq!(json_sub.stream_type, "bookTicker");

    let json_url = json_sub.to_websocket_url("wss://stream.binance.com:9443/stream");
    assert!(json_url.contains("btcusdt@bookTicker"));
    assert!(!json_url.contains("format=sbe"));

    let json_message = json_sub.to_subscribe_message();
    assert!(json_message.contains("btcusdt@bookTicker"));
    assert!(!json_message.contains("format"));

    // 测试SBE订阅配置
    let sbe_sub = MarketDataSubscription::book_ticker("ETHUSDT", MarketDataFormat::Sbe);
    assert_eq!(sbe_sub.format, MarketDataFormat::Sbe);

    let sbe_url = sbe_sub.to_websocket_url("wss://stream.binance.com:9443/stream");
    assert!(sbe_url.contains("ethusdt@bookTicker"));
    assert!(sbe_url.contains("format=sbe"));

    let sbe_message = sbe_sub.to_subscribe_message();
    assert!(sbe_message.contains("ethusdt@bookTicker"));
    assert!(sbe_message.contains("\"format\":\"sbe\""));
}

#[test]
fn test_batch_parsing() {
    let parser = MarketDataParser::new(MarketDataFormat::Json);

    // 测试多行JSON数据
    let batch_json = r#"{"s":"BTCUSDT","b":"50000.0","B":"1.0","a":"50001.0","A":"2.0"}
{"s":"ETHUSDT","b":"3000.0","B":"5.0","a":"3001.0","A":"10.0"}
{"s":"ADAUSDT","b":"0.5","B":"1000.0","a":"0.51","A":"2000.0"}"#;

    let market_data_list = parser.parse_batch(batch_json.as_bytes());
    assert_eq!(market_data_list.len(), 3);

    // 验证第一个消息
    if let Some(first_ticker) = market_data_list[0].as_book_ticker() {
        assert_eq!(first_ticker.symbol, "BTCUSDT");
    }

    // 验证第二个消息
    if let Some(second_ticker) = market_data_list[1].as_book_ticker() {
        assert_eq!(second_ticker.symbol, "ETHUSDT");
    }

    // 验证第三个消息
    if let Some(third_ticker) = market_data_list[2].as_book_ticker() {
        assert_eq!(third_ticker.symbol, "ADAUSDT");
    }
}
