#[cfg(test)]
mod tests {
    use libwebsocket_rs::encoding::sbe::decoder::SbeDecoder;

    #[test]
    fn test_actual_data() {
        // 您实际收到的数据
        let actual_data = [
            32, 0, 17, 39, 1, 0, 0, 0, 47, 107, 239, 114, 31, 55, 6, 0,
            // 这里只是前16字节，实际数据有67字节
            // 为了测试，我们添加一些填充数据到50字节（block_length指定的长度）
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        ];

        println!("测试数据长度: {}", actual_data.len());
        println!("前8字节: {:02X?}", &actual_data[..8]);

        // 解析头部信息
        if actual_data.len() >= 8 {
            let block_length = u16::from_le_bytes([actual_data[0], actual_data[1]]);
            let template_id = u16::from_le_bytes([actual_data[2], actual_data[3]]);
            let schema_id = u16::from_le_bytes([actual_data[4], actual_data[5]]);
            let version = u16::from_le_bytes([actual_data[6], actual_data[7]]);

            println!("解析的头部信息:");
            println!("  block_length: {}", block_length);
            println!("  template_id: {}", template_id);
            println!("  schema_id: {}", schema_id);
            println!("  version: {}", version);
        }

        // 测试SBE检测
        let is_sbe = SbeDecoder::is_sbe_message(&actual_data);
        println!("SBE检测结果: {}", is_sbe);

        if is_sbe {
            println!("✓ 成功识别为SBE格式！");

            // 尝试解码
            if let Some(sbe_message) = SbeDecoder::decode(&actual_data) {
                println!("✓ SBE消息解码成功: {:?}", sbe_message);
            } else {
                println!("✗ SBE消息解码失败");
            }
        } else {
            println!("✗ 未能识别为SBE格式");
        }
    }
}
