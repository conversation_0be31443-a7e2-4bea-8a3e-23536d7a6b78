# libwebsocket-rs

A high-performance WebSocket library for Rust, optimized for low-latency trading applications with support for both JSON and SBE (Simple Binary Encoding) market data formats.

## Features

- **High Performance**: Optimized for low-latency trading applications
- **WebSocket Support**: Full WebSocket client implementation
- **Market Data Processing**: Efficient parsing of market data formats
  - **JSON Format**: Traditional JSON message parsing
  - **SBE Format**: High-performance Simple Binary Encoding support (12.8x faster than JSON)
- **Arbitrage Detection**: Built-in arbitrage opportunity detection
- **Order Management**: Complete order lifecycle management
- **Memory Efficient**: Zero-copy parsing and minimal allocations
- **Auto Format Detection**: Intelligent detection between JSON and SBE formats

## SBE Support

This library now includes comprehensive support for SBE (Simple Binary Encoding), providing significant performance improvements:

- **12.8x faster** parsing compared to JSON
- **24.7% smaller** message size
- Zero-copy parsing for maximum efficiency
- Automatic format detection
- Batch processing support
- **ARM64/AArch64 NEON optimizations** for enhanced performance on ARM processors
- **x86_64 SSE/AVX optimizations** for Intel/AMD processors

### Performance Comparison

| Format | Parse Time | Message Size | Performance Gain |
|--------|------------|--------------|------------------|
| JSON   | ~158 ns    | 85 bytes     | Baseline         |
| SBE    | ~12.3 ns   | 64 bytes     | 12.8x faster     |

### ARM64 Architecture Optimizations

This library includes specialized optimizations for ARM64/AArch64 processors:

- **NEON SIMD Instructions**: Utilizes ARM64's 128-bit NEON registers for parallel data processing
- **Optimized Memory Access**: ARM64-specific memory loading patterns for better cache utilization
- **Batch Processing**: NEON-optimized batch parsing for processing multiple SBE messages simultaneously
- **Automatic Architecture Detection**: Runtime selection of optimal implementation based on target architecture

#### Architecture-Specific Features

| Architecture | SIMD Technology | Key Optimizations |
|--------------|-----------------|-------------------|
| ARM64/AArch64| NEON           | `vld1q_u8`, `vceqq_u8`, `vgetq_lane_u8` |
| x86_64       | SSE/AVX        | `_mm_loadu_si128`, `_mm_cmpeq_epi8` |
| Other        | Generic        | Standard memory operations |

## Quick Start

### Basic Usage

```rust
use libwebsocket_rs::encoding::market_data::{MarketDataParser, MarketDataFormat};

// Create parser for SBE format
let parser = MarketDataParser::new(MarketDataFormat::Sbe);

// Parse SBE message
if let Some(market_data) = parser.parse(sbe_binary_data) {
    if let Some(book_ticker) = market_data.as_book_ticker() {
        println!("Symbol: {}, Bid: {}, Ask: {}",
                 book_ticker.symbol,
                 book_ticker.bid_price,
                 book_ticker.ask_price);
    }
}
```

### Auto Format Detection

```rust
use libwebsocket_rs::encoding::market_data::MarketDataParser;

// Automatically detect and parse message format
if let Some(market_data) = MarketDataParser::parse_auto_detect(raw_data) {
    // Handle both JSON and SBE formats seamlessly
}
```

### ARM64 Optimized Parsing

```rust
use libwebsocket_rs::encoding::sbe::book_ticker::{
    parse_sbe_bookticker_simd_auto,
    parse_sbe_bookticker_batch_auto,
};

// Single message parsing with automatic architecture optimization
if let Some(ticker) = parse_sbe_bookticker_simd_auto(sbe_data) {
    println!("Symbol: {}, Bid: {}", ticker.symbol, ticker.bid_price);
}

// Batch processing with SIMD optimization
let tickers = parse_sbe_bookticker_batch_auto(batch_data);
for ticker in tickers {
    println!("Processed: {}", ticker.symbol);
}
```

## Examples

Run the SBE usage guide example:

```bash
cargo run --example sbe_usage_guide
```

Run the market data example:

```bash
cargo run --example sbe_market_data
```

## Benchmarks

Run performance benchmarks:

```bash
cargo bench --bench sbe_performance_bench
```

## Testing

Run all tests:

```bash
cargo test
```

Run SBE-specific tests:

```bash
cargo test sbe
```

## Documentation

### Core Documentation
- [SBE Support Guide](docs/SBE_SUPPORT.md) - Comprehensive guide to using SBE features
- [Implementation Summary](docs/SBE_IMPLEMENTATION_SUMMARY.md) - Technical details and performance analysis

### Setup and Deployment
- [Quick Start Guide](docs/2025-06-23_QUICK_START.md) - 5-minute setup guide for ARB system
- [Deployment Integration](docs/2025-06-23_DEPLOYMENT_INTEGRATION.md) - Complete deployment automation system
- [ARB Runner Setup](docs/2025-06-23_ARB_RUNNER_README.md) - Process monitoring and management
- [Tmux Setup Guide](docs/2025-06-23_ARB_TMUX_SETUP_COMPLETE.md) - Complete tmux integration setup

### Trading and Orders
- [Trading Fees System](docs/2025-06-23_TRADING_FEES_README.md) - Static trading fee structure
- [Arbitrage Fees Update](docs/2025-06-23_ARBITRAGE_FEES_UPDATE.md) - Dynamic fee calculation system
- [IOC Limit Orders](docs/2025-06-23_LIMIT_ORDER_IOC_UPDATE.md) - Limit order implementation
- [IOC Order Fix](docs/2025-06-23_IOC_LIMIT_ORDER_FIX.md) - Monitor processing logic fixes

### Performance and Monitoring
- [SBE Latency Detector](docs/2025-06-23_SBE_LATENCY_DETECTOR_UPGRADE.md) - SBE-based latency monitoring
- [Development Issues](docs/2025-06-23_issues.md) - Known issues and debugging notes

## Autobahn WebSocket Testing

For WebSocket compliance testing:

1. ```git clone https://github.com/crossbario/autobahn-testsuite.git <clone_dir>```
2. ```cd <clone_dir>/docker```
3. ```docker run -it --rm -v "${PWD}/config:/config" -v "${PWD}/reports:/reports" -p 9001:9001 --name fuzzingserver crossbario/autobahn-testsuite```
4. ```cargo test --package libwebsocket --lib -- tests::test_antubahn_testsuite --exact --nocapture```
5. ```check the test reports in <clone_dir>/reports```
