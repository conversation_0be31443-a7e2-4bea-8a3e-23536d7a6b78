use crate::Result;
use std::net::{SocketAddr, ToSocketAddrs};

#[derive(Clone)]
pub struct Url {
    pub scheme: String,
    pub host: String,
    pub port: u16,
    pub path: String,
    pub is_http: bool,
    pub socket_addr: Option<SocketAddr>,
}

impl Url {
    pub fn to_socket_addr(&self) -> Result<Vec<SocketAddr>> {
        Ok(format!("{}:{}", self.host, self.port)
            .to_socket_addrs()?
            .collect())
    }
}

impl std::fmt::Display for Url {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(
            f,
            "{}://{}:{}{}",
            self.scheme, self.host, self.port, self.path
        )
    }
}

impl<T> From<T> for Url
where
    T: AsRef<str>,
{
    fn from(input: T) -> Self {
        let input = input.as_ref();
        // Split the scheme
        let mut parts = input.splitn(2, "://");
        let scheme = parts.next().unwrap_or("");
        let rest = parts.next().unwrap_or("");

        // Find the path separator
        let (authority, path) = match rest.find('/') {
            Some(index) => (&rest[..index], &rest[index..]),
            None => (rest, "/"),
        };

        // Split host and port
        let mut host = authority;
        let mut port = match scheme {
            "http" => 80,
            "https" => 443,
            "ws" => 80,
            "wss" => 443,
            _ => 0, // Unknown scheme
        };

        if let Some(colon_index) = authority.rfind(':') {
            // Check if the colon is part of an IPv6 address
            if !authority.contains(']') || authority.rfind(']').unwrap_or(0) < colon_index {
                host = &authority[..colon_index];
                port = authority[colon_index + 1..].parse().unwrap_or(port);
            }
        }

        // Remove brackets from IPv6 addresses
        if host.starts_with('[') && host.ends_with(']') {
            host = &host[1..host.len() - 1];
        }

        let scheme_str = scheme.to_string();
        let host_str = host.to_string();
        let path_str = path.to_string();

        Url {
            scheme: scheme_str,
            host: host_str,
            port,
            path: path_str,
            is_http: scheme == "http" || scheme == "https",
            socket_addr: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_url_parsing() {
        let url = Url::from("https://example.com:8080/path/to/resource");
        assert_eq!(url.scheme, "https");
        assert_eq!(url.host, "example.com");
        assert_eq!(url.port, 8080);
        assert_eq!(url.path, "/path/to/resource");
        assert_eq!(url.is_http, true);
    }
}
