use core::fmt::Debug;
use core::mem::MaybeUninit;
use core::ptr;

#[macro_export]
macro_rules! next_pos {
    ($pos:expr, $size:expr, $mask:expr) => {
        ($pos + $size) & $mask
    };
}

pub struct CircularBuffer<const N: usize> {
    buffer: MaybeUninit<[u8; N]>,
    read_pos: usize,
    read_cur: usize,
    write_pos: usize,
}

impl<const N: usize> Debug for CircularBuffer<N> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let (first, second) = self.as_slices();
        let mut buf = Vec::with_capacity(first.len() + second.len());
        buf.extend_from_slice(first);
        buf.extend_from_slice(second);
        let buffer_str = String::from_utf8_lossy(&buf).to_string();

        f.debug_struct("CircularBuffer")
            .field("buffer", &buffer_str)
            .field("read_pos", &self.read_pos)
            .field("read_cursor", &self.read_cur)
            .field("write_pos", &self.write_pos)
            .field("capacity", &N)
            .finish()
    }
}

impl<const N: usize> CircularBuffer<N> {
    const MASK: usize = N - 1;
    const CAP: usize = N - 1;

    pub fn new() -> Self {
        assert!(
            N.is_power_of_two() && N > 1,
            "RingBuf capacity must be a power of two and > 1"
        );
        Self {
            buffer: MaybeUninit::uninit(),
            read_pos: 0,
            read_cur: 0,
            write_pos: 0,
        }
    }

    #[inline(always)]
    pub fn capacity(&self) -> usize {
        Self::CAP
    }

    #[inline(always)]
    pub fn len(&self) -> usize {
        (self.write_pos.wrapping_sub(self.read_pos)) & Self::MASK
    }

    #[inline(always)]
    pub fn pending_len(&self) -> usize {
        (self.write_pos.wrapping_sub(self.read_cur)) & Self::MASK
    }

    #[inline(always)]
    pub fn is_empty(&self) -> bool {
        self.read_pos == self.write_pos
    }

    #[inline(always)]
    pub fn is_full(&self) -> bool {
        next_pos!(self.write_pos, 1, Self::MASK) == self.read_pos
    }

    #[inline(always)]
    pub fn clear(&mut self) {
        self.read_pos = 0;
        self.read_cur = 0;
        self.write_pos = 0;
    }

    #[inline(always)]
    pub fn put_u8(&mut self, data: u8) {
        assert!(!self.is_full(), "circular buffer overflow");
        unsafe {
            let dst = self.buffer.as_mut_ptr().cast::<u8>().add(self.write_pos);
            ptr::write(dst, data);
        }
        self.write_pos = next_pos!(self.write_pos, 1, Self::MASK);
    }

    #[inline(always)]
    pub fn put_u64(&mut self, data: u64) {
        self.put_bytes(&data.to_be_bytes());
    }

    #[inline(always)]
    pub fn put_u16(&mut self, data: u16) {
        self.put_bytes(&data.to_be_bytes());
    }

    #[inline(always)]
    pub fn put_u32(&mut self, data: u32) {
        self.put_bytes(&data.to_be_bytes());
    }

    #[inline(always)]
    pub fn put<T: AsRef<[u8]>>(&mut self, data: T) {
        self.put_bytes(data.as_ref());
    }

    #[inline(always)]
    fn put_bytes(&mut self, slice: &[u8]) {
        assert!(slice.len() <= Self::CAP - self.len(), "overflow");
        let first = core::cmp::min(slice.len(), N - self.write_pos);
        unsafe {
            let base = self.buffer.as_mut_ptr().cast::<u8>();
            ptr::copy_nonoverlapping(slice.as_ptr(), base.add(self.write_pos), first);
            if slice.len() != first {
                ptr::copy_nonoverlapping(slice.as_ptr().add(first), base, slice.len() - first);
            }
        }
        self.write_pos = next_pos!(self.write_pos, slice.len(), Self::MASK);
    }

    #[inline(always)]
    pub fn peek(&self) -> Option<u8> {
        if self.pending_len() == 0 {
            None
        } else {
            Some(unsafe { *self.buffer.as_ptr().cast::<u8>().add(self.read_cur) })
        }
    }

    #[inline(always)]
    pub fn peek_u16_be(&self) -> Option<u16> {
        if self.pending_len() < 2 {
            return None;
        }
        if self.read_cur + 2 <= Self::CAP + 1 {
            unsafe {
                let p = self.buffer.as_ptr().cast::<u8>().add(self.read_cur) as *const u16;
                return Some(u16::from_be(ptr::read_unaligned(p)));
            }
        }

        let mut tmp = MaybeUninit::<[u8; 2]>::uninit();
        unsafe {
            let base = self.buffer.as_ptr().cast::<u8>();
            let first = Self::CAP + 1 - self.read_cur;
            ptr::copy_nonoverlapping(base.add(self.read_cur), tmp.as_mut_ptr() as *mut u8, first);
            ptr::copy_nonoverlapping(base, (tmp.as_mut_ptr() as *mut u8).add(first), 2 - first);
            Some(u16::from_be_bytes(tmp.assume_init()))
        }
    }

    #[inline(always)]
    pub fn peek_u64_be(&self) -> Option<u64> {
        if self.pending_len() < 8 {
            return None;
        }
        if self.read_cur + 8 <= Self::CAP + 1 {
            unsafe {
                let p = self.buffer.as_ptr().cast::<u8>().add(self.read_cur) as *const u64;
                return Some(u64::from_be(ptr::read_unaligned(p)));
            }
        }
        let mut tmp = MaybeUninit::<[u8; 8]>::uninit();
        unsafe {
            let base = self.buffer.as_ptr().cast::<u8>();
            let first = Self::CAP + 1 - self.read_cur;
            ptr::copy_nonoverlapping(base.add(self.read_cur), tmp.as_mut_ptr() as *mut u8, first);
            ptr::copy_nonoverlapping(base, (tmp.as_mut_ptr() as *mut u8).add(first), 8 - first);
            Some(u64::from_be_bytes(tmp.assume_init()))
        }
    }

    #[inline(always)]
    pub fn read_into(&mut self, dst: &mut [u8]) {
        assert!(dst.len() <= self.pending_len(), "not enough data");
        let (seg1, seg2) = self.as_slices();
        unsafe {
            ptr::copy_nonoverlapping(seg1.as_ptr(), dst.as_mut_ptr(), seg1.len());
            if !seg2.is_empty() {
                ptr::copy_nonoverlapping(
                    seg2.as_ptr(),
                    dst.as_mut_ptr().add(seg1.len()),
                    seg2.len(),
                );
            }
        }
        self.advance_and_commit_read(dst.len());
    }

    #[inline(always)]
    pub fn as_slices<'b>(&self) -> (&'b [u8], &'b [u8]) {
        self.slice_internal(self.read_cur)
    }

    #[inline(always)]
    fn slice_internal<'b>(&self, start: usize) -> (&'b [u8], &'b [u8]) {
        let len = (self.write_pos.wrapping_sub(start)) & Self::MASK;
        let first = core::cmp::min(len, N - start);
        unsafe {
            let base = self.buffer.as_ptr().cast::<u8>();
            let a = core::slice::from_raw_parts(base.add(start), first);
            let b = core::slice::from_raw_parts(base, len - first);
            (a, b)
        }
    }

    #[inline(always)]
    pub fn as_mut_slices<'b>(&mut self) -> (&'b mut [u8], &'b mut [u8]) {
        let free = Self::CAP - self.len();
        if free == 0 {
            return (&mut [], &mut []);
        }

        let first = core::cmp::min(free, N - self.write_pos);
        unsafe {
            let base = self.buffer.as_mut_ptr().cast::<u8>();
            let slice1 = core::slice::from_raw_parts_mut(base.add(self.write_pos), first);
            if free == first {
                (slice1, &mut [])
            } else {
                let slice2 = core::slice::from_raw_parts_mut(base, free - first);
                (slice1, slice2)
            }
        }
    }

    pub fn advance_read(&mut self, n: usize) {
        assert!(n <= self.pending_len(), "advance > pending");
        self.read_cur = next_pos!(self.read_cur, n, Self::MASK);
    }

    pub fn commit_read(&mut self) {
        self.read_pos = self.read_cur;
    }

    pub fn advance_and_commit_read(&mut self, size: usize) {
        self.advance_read(size);
        self.commit_read();
    }

    pub fn reset_read(&mut self) {
        self.read_cur = self.read_pos;
    }

    pub fn advance_write(&mut self, size: usize) {
        self.write_pos = next_pos!(self.write_pos, size, Self::MASK);
    }
}

impl<const N: usize> From<Vec<i32>> for CircularBuffer<N> {
    fn from(data: Vec<i32>) -> Self {
        let mut res = CircularBuffer::new();
        let buffer: Vec<u8> = data
            .into_iter()
            .flat_map(|x| x.to_be_bytes().to_vec())
            .collect();
        res.put(buffer);
        res
    }
}

impl<const N: usize> From<Vec<u8>> for CircularBuffer<N> {
    fn from(data: Vec<u8>) -> Self {
        let mut res = CircularBuffer::new();
        res.put(data);
        res
    }
}

impl<const N: usize> From<&[u8]> for CircularBuffer<N> {
    fn from(data: &[u8]) -> Self {
        let mut res = CircularBuffer::new();
        res.put(data);
        res
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_put() {
        let mut buffer = CircularBuffer::<16>::new();

        // 测试切片
        buffer.put(&[1, 2, 3, 4]);
        assert_eq!(buffer.len(), 4);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1, &[1, 2, 3, 4]);
        assert_eq!(slice2.len(), 0);

        // 测试固定大小数组
        let arr: [u8; 3] = [5, 6, 7];
        buffer.put(arr);
        assert_eq!(buffer.len(), 7);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1, &[1, 2, 3, 4, 5, 6, 7]);
        assert_eq!(slice2.len(), 0);

        // 测试向量
        let vec = vec![8, 9];
        buffer.put(vec);
        assert_eq!(buffer.len(), 9);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1, &[1, 2, 3, 4, 5, 6, 7, 8, 9]);
        assert_eq!(slice2.len(), 0);

        // 测试循环写入
        buffer.clear();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);
        buffer.advance_and_commit_read(3);
        buffer.put(&[16, 17]);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1, &[4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]);
        assert_eq!(slice2, &[17]);
    }

    #[test]
    fn test_commit_read() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.len(), 10);
        buffer.advance_and_commit_read(10);
        assert_eq!(buffer.len(), 0);
    }

    #[test]
    fn test_as_slices() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.len(), 10);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1.len(), 10);
        assert_eq!(slice2.len(), 0);
        assert_eq!(slice1[0], 1);
        assert_eq!(slice1[1], 2);
        assert_eq!(slice1[2], 3);
        buffer.advance_and_commit_read(10);
        buffer.put(&[11, 12, 13, 15, 16, 17, 18]);
        assert_eq!(buffer.len(), 7);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1.len(), 6);
        assert_eq!(slice2.len(), 1);
        assert_eq!(slice1[0], 11);
        assert_eq!(slice1[1], 12);
        assert_eq!(slice1[2], 13);
        assert_eq!(slice2[0], 18);
    }

    #[test]
    fn test_as_mut_slice() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.len(), 10);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 5);
        assert_eq!(slice2.len(), 0);
        buffer.advance_and_commit_read(5);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 6);
        assert_eq!(slice2.len(), 4);
        buffer.advance_and_commit_read(5);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 6);
        assert_eq!(slice2.len(), 9);
        buffer.put(&[11, 12, 13]);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 3);
        assert_eq!(slice2.len(), 9);
        buffer.advance_and_commit_read(3);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 3);
        assert_eq!(slice2.len(), 12);
    }

    #[test]
    fn test_put_and_commit() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7]);
        assert_eq!(buffer.len(), 7);
        buffer.advance_and_commit_read(3);
        assert_eq!(buffer.len(), 4);
        buffer.advance_and_commit_read(4);
        assert_eq!(buffer.len(), 0);
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1.len(), 9);
        assert_eq!(slice1.as_ref(), &[1, 2, 3, 4, 5, 6, 7, 8, 9]);
        assert_eq!(slice2.len(), 1);
        assert_eq!(slice2.as_ref(), &[10]);
        assert_eq!(buffer.len(), 10);
        buffer.advance_and_commit_read(10);
        assert_eq!(buffer.len(), 0);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 15);
        assert_eq!(slice2.len(), 0);
        for i in 1..11 {
            slice1[i - 1] = i as u8;
        }
        buffer.advance_write(10);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1.as_ref(), &[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(slice2.len(), 0);
        assert_eq!(buffer.len(), 10);
        buffer.advance_and_commit_read(10);
        let (slice1, slice2) = buffer.as_mut_slices();
        assert_eq!(slice1.len(), 5);
        assert_eq!(slice2.len(), 10);
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        let (slice1, slice2) = buffer.as_slices();
        assert_eq!(slice1.len(), 5);
        assert_eq!(slice1.as_ref(), &[1, 2, 3, 4, 5]);
        assert_eq!(slice2.as_ref(), &[6, 7, 8, 9, 10]);
        assert_eq!(buffer.len(), 10);
    }

    #[test]
    fn test_peek() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.peek(), Some(1));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(2));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(3));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(4));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(5));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(6));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(7));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(8));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(9));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), Some(10));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek(), None);
    }

    #[test]
    fn test_peek_u16_be() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.peek_u16_be(), Some(258));
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek_u16_be(), Some(515));
        buffer.advance_and_commit_read(9);
        buffer.put(&[1, 2, 3, 4, 1, 1, 2, 8, 9, 10]);
        buffer.advance_and_commit_read(5);
        assert_eq!(buffer.peek_u16_be(), Some(258));
    }

    #[test]
    fn test_peek_u64_be() {
        let mut buffer = CircularBuffer::<16>::new();
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.peek_u64_be(), Some(72623859790382856));
        buffer.advance_and_commit_read(10);
        buffer.put(&[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
        assert_eq!(buffer.peek_u64_be(), Some(72623859790382856));
    }
}
