#![deny(
    trivial_casts,
    trivial_numeric_casts,
    unstable_features,
    unused_import_braces
)]

mod connection;
pub mod io;
pub mod message;
pub mod result;
mod setting;
mod stream;
pub mod utils;

pub use self::io::WebSocket;
pub use self::message::Message;
pub use self::message::close_code::CloseCode;
pub use self::message::opcode::OpCode;
pub use self::result::Kind as ErrorKind;
pub use self::result::{Erro<PERSON>, Result};
pub use self::setting::Settings;
