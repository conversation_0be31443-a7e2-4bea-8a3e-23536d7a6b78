use super::Message;
use super::Settings;
use super::connection::Connection;
use super::result::{Error, Kind, Result};
use super::utils::circular_buffer::CircularBuffer;
use super::utils::url::Url;
use core::fmt;
use mio;
use mio::Events;
use mio::{Interest, Poll, Token};
use std::collections::HashMap;

const MAX_EVENTS: usize = 1024;

#[derive(Debug)]
enum State {
    Active,
    Inactive,
}

impl State {
    fn is_active(&self) -> bool {
        match *self {
            State::Active => true,
            State::Inactive => false,
        }
    }
}

pub enum CallbackData {
    ConnectionOpen(Token),
    Message(Token, Message<'static>),
    ConnectionClose(Token),
    ConnectionError(Token, Error),
}

impl fmt::Debug for CallbackData {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CallbackData::ConnectionOpen(token) => {
                write!(f, "CallbackData::ConnectionOpen({:?})", token)
            }
            CallbackData::Message(token, msg) => {
                write!(f, "CallbackData::Message({:?}, {:?})", token, msg)
            }
            CallbackData::ConnectionClose(token) => {
                write!(f, "CallbackData::ConnectionClose({:?})", token)
            }
            CallbackData::ConnectionError(token, err) => {
                write!(f, "CallbackData::ConnectionError({:?}, {:?})", token, err)
            }
        }
    }
}

pub struct WebSocketHandle<const N: usize> {
    connections: HashMap<Token, Connection<N>>,
    state: State,
}

impl<const N: usize> WebSocketHandle<N> {
    pub fn send_message<M>(&mut self, token: Token, msg: M) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let msg = msg.into();
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.write_message(msg)?;
        Ok(())
    }

    #[inline]
    pub fn get_write_buf(&mut self, token: Token) -> Result<&mut CircularBuffer<N>> {
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        Ok(conn.write_buf_mut())
    }

    #[inline]
    pub fn trigger_write(&mut self, token: Token) -> Result<()> {
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.write()
    }

    #[inline]
    pub fn send_messages<M>(&mut self, token: Token, msgs: Vec<M>) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.send_messages(msgs)?;
        Ok(())
    }

    pub fn send_message_n<const NUM: usize, M>(
        &mut self,
        token: Token,
        msgs: [M; NUM],
    ) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.write_message_n(msgs)?;
        Ok(())
    }

    pub fn close_conn(&mut self, token: Token) -> Result<()> {
        let conn = self.connections.get_mut(&token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.shutdown()?;
        Ok(())
    }

    pub fn stop(&mut self) {
        self.state = State::Inactive;
    }
}

pub struct WebSocket<F, const N: usize>
where
    F: FnMut(&mut WebSocketHandle<N>, CallbackData) -> Result<()>,
{
    handle: WebSocketHandle<N>,
    settings: Settings,
    poll: Poll,
    events: Events,
    callback: F,
}

impl<F, const N: usize> WebSocket<F, N>
where
    F: FnMut(&mut WebSocketHandle<N>, CallbackData) -> Result<()>,
{
    pub fn new(settings: Settings, callback: F) -> Result<Self> {
        Ok(Self {
            handle: WebSocketHandle {
                connections: HashMap::with_capacity(settings.max_connections),
                state: State::Inactive,
            },
            settings,
            poll: Poll::new()?,
            events: mio::Events::with_capacity(MAX_EVENTS),
            callback,
        })
    }

    pub fn handle_mut(&mut self) -> &mut WebSocketHandle<N> {
        &mut self.handle
    }

    pub fn connect<T>(&mut self, url: T, token: Token) -> Result<()>
    where
        T: Into<Url>,
    {
        let url: Url = url.into();
        self.inner_connect(url, token, None)?;
        Ok(())
    }

    pub fn connect_with_headers<T>(
        &mut self,
        url: T,
        token: Token,
        headers: std::collections::HashMap<String, String>,
    ) -> Result<()>
    where
        T: Into<Url>,
    {
        let url: Url = url.into();
        self.inner_connect(url, token, Some(headers))?;
        Ok(())
    }

    fn inner_connect(
        &mut self,
        url: Url,
        tok: Token,
        headers: Option<std::collections::HashMap<String, String>>,
    ) -> Result<()> {
        if self.handle.connections.len() >= self.settings.max_connections {
            return Err(Error::new(
                Kind::Capacity,
                "Unable to add another connection to the event loop.",
            ));
        }

        match Connection::connect_with_headers(url, self.settings.clone(), headers) {
            Ok(mut conn) => {
                self.poll
                    .registry()
                    .register(
                        conn.socket_mut(),
                        tok,
                        Interest::READABLE | Interest::WRITABLE,
                    )
                    .map_err(|err| {
                        Error::new(
                            Kind::Internal,
                            format!(
                                "Encountered error when trying to build WebSocket connection {}",
                                err
                            ),
                        )
                    })?;
                self.handle.connections.insert(tok, conn);
                Ok(())
            }
            Err(e) => Err(e),
        }
    }

    pub fn handle_conn_close(&mut self, token: Token) -> Result<()> {
        let conn = match self.handle.connections.get_mut(&token) {
            Some(conn) => conn,
            None => {
                return Err(Error::new(
                    Kind::Internal,
                    format!("Connection not found for token: {:?}", token),
                ));
            }
        };
        match self.poll.registry().deregister(conn.socket_mut()) {
            Ok(_) => {}
            Err(err) => {
                return Err(Error::new(
                    Kind::Internal,
                    format!(
                        "Encountered error when trying to deregister WebSocket connection {}",
                        err
                    ),
                ));
            }
        }
        let _ = conn.shutdown();
        if self.settings.auto_reconnect {
            let url = conn.url.clone();
            self.inner_connect(url, token, None)?;
        } else {
            self.handle.connections.remove(&token);
        }
        Ok(())
    }

    pub fn run(&mut self) -> Result<()> {
        self.handle.state = State::Active;
        self.event_loop()?;
        self.handle.state = State::Inactive;
        Ok(())
    }

    #[inline]
    fn event_loop(&mut self) -> Result<()> {
        while self.handle.state.is_active() {
            if self.handle.connections.is_empty() && self.settings.stop_when_no_connection {
                break;
            }
            self.poll
                .poll(&mut self.events, self.settings.event_loop_timeout)?;
            self.handle_events()?;
        }
        Ok(())
    }

    fn handle_events(&mut self) -> Result<()> {
        for event in self.events.iter() {
            let token = event.token();
            if event.is_readable() {
                loop {
                    let conn = match self.handle.connections.get_mut(&token) {
                        Some(conn) => conn,
                        None => {
                            return Err(Error::new(
                                Kind::Internal,
                                format!("Connection not found for token: {:?}", token),
                            ));
                        }
                    };
                    match conn.read() {
                        Ok(Some(Message::Open)) => {
                            (self.callback)(&mut self.handle, CallbackData::ConnectionOpen(token))?;
                        }
                        Ok(Some(msgs)) => {
                            (self.callback)(&mut self.handle, CallbackData::Message(token, msgs))?;
                        }
                        Ok(None) => {
                            break;
                        }
                        Err(err) => {
                            let _ = conn.shutdown(); // we make shutdown err silence here, we cannot do anything
                            match err.kind {
                                Kind::ClosedByPeer | Kind::Closing => {
                                    (self.callback)(
                                        &mut self.handle,
                                        CallbackData::ConnectionClose(token),
                                    )?;
                                }
                                _ => {
                                    (self.callback)(
                                        &mut self.handle,
                                        CallbackData::ConnectionError(token, err),
                                    )?;
                                }
                            }
                            break;
                        }
                    }
                }
            }
            if event.is_writable() {
                if let Some(conn) = self.handle.connections.get_mut(&token) {
                    conn.write()?;
                } else {
                    return Err(Error::new(
                        Kind::Internal,
                        format!("Connection not found for token: {:?}", token),
                    ));
                }
            }
        }
        let to_close_tokens: Vec<_> = self
            .handle
            .connections
            .iter_mut()
            .filter_map(|(t, conn)| {
                if conn.is_closing() || conn.is_closed() {
                    Some(*t)
                } else {
                    None
                }
            })
            .collect();

        for t in to_close_tokens {
            self.handle_conn_close(t)?;
        }
        Ok(())
    }
}
