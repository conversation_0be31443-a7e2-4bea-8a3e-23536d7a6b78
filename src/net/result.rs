use std::borrow::Cow;
use std::convert::{From, Into};
use std::error::<PERSON>rror as StdError;
use std::fmt::{self, Display};
use std::io;
use std::num::ParseIntError;
use std::result::Result as StdResult;
use std::str::Utf8Error;
use std::string::FromUtf8Error;

use httparse;
use rustls::Error as RustlsError;

pub type Result<T> = StdResult<T, Error>;

/// The type of an error, which may indicate other kinds of errors as the underlying cause.
#[derive(Debug)]
pub enum Kind {
    Internal,
    Capacity,
    Protocol,
    Encoding(Utf8Error),
    Io(io::Error),
    Http(httparse::Error),
    TlsError(RustlsError),
    Custom(Box<dyn StdError + Send + Sync>),
    Closing,
    Closed,
    IncompleteHttpResponse,
    Unknown,
    HttpRequestInProcess,
    AddrParseError(std::net::AddrParseError),
    NetWorkError,
    ClosedByPeer,
    WebsocketHandshakeFailed,
    HttpResponseParseFailed,
}

impl Display for Kind {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match *self {
            Kind::IncompleteHttpResponse => {
                write!(f, "Incomplete HTTP Response Body")
            }
            Kind::HttpRequestInProcess => {
                write!(f, "HTTP Request in Process")
            }
            Kind::AddrParseError(ref err) => {
                write!(f, "Address Parse Error: {}", err)
            }
            Kind::NetWorkError => {
                write!(f, "Network Error")
            }
            Kind::Internal => write!(f, "Internal Application Error"),
            Kind::Capacity => write!(f, "WebSocket at Capacity"),
            Kind::Protocol => write!(f, "WebSocket Protocol Error"),
            Kind::Encoding(_) => write!(f, "WebSocket Encoding Error"),
            Kind::Io(_) => write!(f, "IO Error"),
            Kind::Http(_) => write!(f, "HTTP Error"),
            Kind::TlsError(_) => write!(f, "TLS Error"),
            Kind::Custom(_) => write!(f, "Custom Error"),
            Kind::Closing => write!(f, "Connection Closing"),
            Kind::Closed => write!(f, "Connection Closed"),
            Kind::ClosedByPeer => write!(f, "Connection Closed by Peer"),
            Kind::Unknown => write!(f, "Unknown Error"),
            Kind::WebsocketHandshakeFailed => write!(f, "Websocket Handshake Failed"),
            Kind::HttpResponseParseFailed => write!(f, "HTTP Response Parse Failed"),
        }
    }
}

/// A struct indicating the kind of error that has occurred and any precise details of that error.
pub struct Error {
    pub kind: Kind,
    pub details: Cow<'static, str>,
}

impl Error {
    pub fn new<I>(kind: Kind, details: I) -> Error
    where
        I: Into<Cow<'static, str>>,
    {
        Error {
            kind,
            details: details.into(),
        }
    }
}

impl fmt::Debug for Error {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        if !self.details.is_empty() {
            write!(f, "Error <{:?}>: {}", self.kind, self.details)
        } else {
            write!(f, "Error <{:?}>", self.kind)
        }
    }
}

impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        if !self.details.is_empty() {
            write!(f, "Error {}: {}", self.kind, self.details)
        } else {
            write!(f, "Error {}", self.kind)
        }
    }
}

impl From<io::Error> for Error {
    fn from(err: io::Error) -> Error {
        Error::new(Kind::Io(err), "")
    }
}

impl From<Utf8Error> for Error {
    fn from(err: Utf8Error) -> Error {
        Error::new(Kind::Encoding(err), "")
    }
}

impl From<FromUtf8Error> for Error {
    fn from(err: FromUtf8Error) -> Error {
        Error::new(Kind::Encoding(err.utf8_error()), "")
    }
}

impl From<ParseIntError> for Error {
    fn from(err: ParseIntError) -> Error {
        Error::new(Kind::Internal, err.to_string())
    }
}

impl From<httparse::Error> for Error {
    fn from(err: httparse::Error) -> Error {
        let details = match err {
            httparse::Error::HeaderName => "Invalid byte in header name.",
            httparse::Error::HeaderValue => "Invalid byte in header value.",
            httparse::Error::NewLine => "Invalid byte in new line.",
            httparse::Error::Status => "Invalid byte in Response status.",
            httparse::Error::Token => "Invalid byte where token is required.",
            httparse::Error::TooManyHeaders => {
                "Parsed more headers than provided buffer can contain."
            }
            httparse::Error::Version => "Invalid byte in HTTP version.",
        };

        Error::new(Kind::Http(err), details)
    }
}

impl From<RustlsError> for Error {
    fn from(err: RustlsError) -> Error {
        Error::new(Kind::TlsError(err), "")
    }
}

impl<B> From<Box<B>> for Error
where
    B: StdError + Send + Sync + 'static,
{
    fn from(err: Box<B>) -> Error {
        Error::new(Kind::Custom(err), "")
    }
}

impl std::error::Error for Error {}
