pub mod close_code;
pub mod frame;
pub mod http;
pub mod opcode;

use core::fmt;
use std::borrow::Cow;

use crate::net::{CloseCode, Error, Result};

pub enum Message<'m> {
    Open,
    WebsocketPayload(Cow<'m, [u8]>),
    WebsocketSbeBinary(Cow<'m, [u8]>),
    WebsocketPing(Cow<'m, [u8]>),
    WebsocketPong(Cow<'m, [u8]>),
    HttpRequest(http::HttpRequest<'m>),
    HttpResponse(Cow<'m, http::HttpResponse<'m>>),
    HttpClose,
    HandshakeResponse(http::HttpResponse<'m>),
    HandshakeRequest(http::HttpRequest<'m>),
    WebsocketClose(CloseCode, String),
}

impl<'m> Message<'m> {
    pub fn http_request(from_str: &'m str) -> Result<Message<'m>> {
        Ok(Message::HttpRequest(http::HttpRequest::from(from_str)))
    }
}

use self::frame::Frame;

use super::OpCode;

impl<'m> Message<'m> {
    pub fn close<S>(code: CloseCode, string: S) -> Message<'m>
    where
        S: Into<String>,
    {
        Message::WebsocketClose(code, string.into())
    }

    pub fn opcode(&self) -> OpCode {
        match *self {
            Message::WebsocketPayload(_) => OpCode::Text,
            Message::WebsocketSbeBinary(_) => OpCode::Binary,
            Message::WebsocketPing(_) => OpCode::Ping,
            Message::WebsocketPong(_) => OpCode::Pong,
            Message::WebsocketClose(..) => OpCode::Close,
            _ => OpCode::Bad,
        }
    }

    pub fn as_request(&self) -> Result<&http::HttpRequest> {
        match *self {
            Message::HttpRequest(ref req) => Ok(req),
            _ => Err(Error::new(
                super::ErrorKind::Internal,
                "Message is not a Request",
            )),
        }
    }

    pub fn as_response(&self) -> Result<&http::HttpResponse> {
        match *self {
            Message::HttpResponse(ref res) => Ok(res),
            _ => Err(Error::new(
                super::ErrorKind::Internal,
                "Message is not a Response",
            )),
        }
    }

    pub fn as_frame_payload(self) -> Cow<'m, [u8]> {
        match self {
            Message::WebsocketPayload(payload) => payload,
            _ => Cow::Borrowed(b""),
        }
    }
}

impl<'b> From<&'b [u8]> for Message<'b> {
    fn from(data: &'b [u8]) -> Message<'b> {
        Message::WebsocketPayload(data.into())
    }
}

impl<'a> From<Frame<'a>> for Message<'a> {
    fn from(frame: Frame<'a>) -> Message<'a> {
        Message::WebsocketPayload(frame.payload)
    }
}

impl<'a> From<Vec<u8>> for Message<'a> {
    fn from(data: Vec<u8>) -> Message<'a> {
        Message::WebsocketPayload(data.into())
    }
}

impl<'a> From<String> for Message<'a> {
    fn from(data: String) -> Message<'a> {
        Message::WebsocketPayload(data.into_bytes().into())
    }
}

impl fmt::Debug for Message<'_> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Message::Open => write!(f, "Message::Open"),
            Message::WebsocketPayload(payload) => {
                write!(
                    f,
                    "Message::WebsocketPayload({:?})",
                    String::from_utf8_lossy(payload.as_ref())
                )
            }
            Message::WebsocketSbeBinary(payload) => {
                write!(f, "Message::WebsocketSbeBinary({} bytes)", payload.len())
            }
            Message::WebsocketPing(payload) => write!(f, "Message::WebsocketPing({:?})", payload),
            Message::WebsocketPong(payload) => write!(f, "Message::WebsocketPong({:?})", payload),
            Message::WebsocketClose(code, reason) => {
                write!(f, "Message::WebsocketClose({:?}, {:?})", code, reason)
            }
            Message::HttpRequest(request) => write!(f, "Message::HttpRequest({:?})", request),
            Message::HttpResponse(response) => write!(f, "Message::HttpResponse({:?})", response),
            Message::HttpClose => write!(f, "Message::HttpClose"),
            Message::HandshakeResponse(response) => {
                write!(f, "Message::HandshakeResponse({:?})", response)
            }
            Message::HandshakeRequest(request) => {
                write!(f, "Message::HandshakeRequest({:?})", request)
            }
        }
    }
}
