use crate::encoding::book_ticker::{BookTicker, parse_bookticker};
use crate::encoding::sbe::{SbeDecoder, book_ticker::SbeBookTicker, decoder::SbeMessage};

/// 行情数据格式枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MarketDataFormat {
    Json,
    Sbe,
}

impl Default for MarketDataFormat {
    fn default() -> Self {
        MarketDataFormat::Json
    }
}

/// 统一的行情数据结构
#[derive(Debug)]
pub enum MarketData<'a> {
    BookTicker(BookTicker<'a>),
    SbeBookTicker(SbeBookTicker<'a>),
    Trade,
    OrderBookUpdate,
    Heartbeat,
    Unknown,
}

impl<'a> MarketData<'a> {
    /// 获取BookTicker数据（如果是BookTicker类型）
    pub fn as_book_ticker(&self) -> Option<BookTicker<'a>> {
        match self {
            MarketData::BookTicker(ticker) => Some(BookTicker {
                symbol: ticker.symbol,
                bid_price: ticker.bid_price,
                bid_qty: ticker.bid_qty,
                ask_price: ticker.ask_price,
                ask_qty: ticker.ask_qty,
            }),
            MarketData::SbeBookTicker(sbe_ticker) => {
                // 将SBE BookTicker转换为标准BookTicker
                Some(BookTicker {
                    symbol: sbe_ticker.symbol,
                    bid_price: sbe_ticker.bid_price,
                    bid_qty: sbe_ticker.bid_qty,
                    ask_price: sbe_ticker.ask_price,
                    ask_qty: sbe_ticker.ask_qty,
                })
            }
            _ => None,
        }
    }

    /// 获取symbol（如果有的话）
    pub fn symbol(&self) -> Option<&str> {
        match self {
            MarketData::BookTicker(ticker) => Some(ticker.symbol),
            MarketData::SbeBookTicker(ticker) => Some(ticker.symbol),
            _ => None,
        }
    }
}

/// 行情数据解析器
pub struct MarketDataParser {
    format: MarketDataFormat,
}

impl MarketDataParser {
    /// 创建新的行情数据解析器
    pub fn new(format: MarketDataFormat) -> Self {
        Self { format }
    }

    /// 解析行情数据
    ///
    /// # Arguments
    /// * `data` - 原始数据字节
    ///
    /// # Returns
    /// * `Some(MarketData)` - 解析成功的行情数据
    /// * `None` - 解析失败
    pub fn parse<'a>(&self, data: &'a [u8]) -> Option<MarketData<'a>> {
        match self.format {
            MarketDataFormat::Json => {
                // 尝试解析JSON格式的BookTicker
                if let Some(book_ticker) = parse_bookticker(data) {
                    Some(MarketData::BookTicker(book_ticker))
                } else {
                    None
                }
            }
            MarketDataFormat::Sbe => {
                // 解析SBE格式的消息
                if let Some(sbe_message) = SbeDecoder::decode(data) {
                    match sbe_message {
                        SbeMessage::BookTicker(book_ticker) => {
                            Some(MarketData::SbeBookTicker(book_ticker))
                        }
                        SbeMessage::Trade => Some(MarketData::Trade),
                        SbeMessage::OrderBookUpdate => Some(MarketData::OrderBookUpdate),
                        SbeMessage::Heartbeat => Some(MarketData::Heartbeat),
                        SbeMessage::Unknown => Some(MarketData::Unknown),
                    }
                } else {
                    None
                }
            }
        }
    }

    /// 自动检测数据格式并解析
    ///
    /// # Arguments
    /// * `data` - 原始数据字节
    ///
    /// # Returns
    /// * `Some(MarketData)` - 解析成功的行情数据
    /// * `None` - 解析失败
    pub fn parse_auto_detect<'a>(data: &'a [u8]) -> Option<MarketData<'a>> {
        // 首先尝试SBE格式
        if SbeDecoder::is_sbe_message(data) {
            let parser = MarketDataParser::new(MarketDataFormat::Sbe);
            if let Some(market_data) = parser.parse(data) {
                return Some(market_data);
            }
        }

        // 然后尝试JSON格式
        let parser = MarketDataParser::new(MarketDataFormat::Json);
        parser.parse(data)
    }

    /// 批量解析行情数据
    ///
    /// # Arguments
    /// * `data` - 包含多个消息的数据字节
    ///
    /// # Returns
    /// * `Vec<MarketData>` - 解析成功的行情数据列表
    pub fn parse_batch<'a>(&self, data: &'a [u8]) -> Vec<MarketData<'a>> {
        match self.format {
            MarketDataFormat::Json => {
                // JSON格式通常是单个消息，但可以尝试按行分割
                let mut results = Vec::new();
                for line in data.split(|&b| b == b'\n') {
                    if !line.is_empty() {
                        if let Some(market_data) = self.parse(line) {
                            results.push(market_data);
                        }
                    }
                }
                results
            }
            MarketDataFormat::Sbe => {
                // SBE格式支持批量解析
                let sbe_messages = SbeDecoder::decode_batch(data);
                sbe_messages
                    .into_iter()
                    .map(|sbe_msg| match sbe_msg {
                        SbeMessage::BookTicker(book_ticker) => {
                            MarketData::SbeBookTicker(book_ticker)
                        }
                        SbeMessage::Trade => MarketData::Trade,
                        SbeMessage::OrderBookUpdate => MarketData::OrderBookUpdate,
                        SbeMessage::Heartbeat => MarketData::Heartbeat,
                        SbeMessage::Unknown => MarketData::Unknown,
                    })
                    .collect()
            }
        }
    }
}

/// 行情订阅配置
#[derive(Debug, Clone)]
pub struct MarketDataSubscription {
    pub symbol: String,
    pub format: MarketDataFormat,
    pub stream_type: String, // 如 "bookTicker", "trade", "depth"
}

impl MarketDataSubscription {
    /// 创建BookTicker订阅
    pub fn book_ticker(symbol: &str, format: MarketDataFormat) -> Self {
        Self {
            symbol: symbol.to_string(),
            format,
            stream_type: "bookTicker".to_string(),
        }
    }

    /// 生成WebSocket订阅URL
    pub fn to_websocket_url(&self, base_url: &str) -> String {
        match self.format {
            MarketDataFormat::Json => {
                format!(
                    "{}?streams={}@{}",
                    base_url,
                    self.symbol.to_lowercase(),
                    self.stream_type
                )
            }
            MarketDataFormat::Sbe => {
                // SBE格式可能需要不同的URL参数
                format!(
                    "{}?streams={}@{}&format=sbe",
                    base_url,
                    self.symbol.to_lowercase(),
                    self.stream_type
                )
            }
        }
    }

    /// 生成订阅请求消息
    pub fn to_subscribe_message(&self) -> String {
        match self.format {
            MarketDataFormat::Json => {
                format!(
                    r#"{{"method":"SUBSCRIBE","params":["{}@{}"],"id":1}}"#,
                    self.symbol.to_lowercase(),
                    self.stream_type
                )
            }
            MarketDataFormat::Sbe => {
                format!(
                    r#"{{"method":"SUBSCRIBE","params":["{}@{}"],"format":"sbe","id":1}}"#,
                    self.symbol.to_lowercase(),
                    self.stream_type
                )
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_market_data_parser_json() {
        let parser = MarketDataParser::new(MarketDataFormat::Json);
        let json_data = r#"{"s":"BTCUSDT","b":"50000.0","B":"1.0","a":"50001.0","A":"2.0"}"#;

        let market_data = parser.parse(json_data.as_bytes()).unwrap();
        if let Some(book_ticker) = market_data.as_book_ticker() {
            assert_eq!(book_ticker.symbol, "BTCUSDT");
            assert_eq!(book_ticker.bid_price, 50000.0);
            assert_eq!(book_ticker.ask_price, 50001.0);
        } else {
            panic!("Expected BookTicker");
        }
    }

    #[test]
    fn test_market_data_subscription() {
        let subscription = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Json);

        assert_eq!(subscription.symbol, "BTCUSDT");
        assert_eq!(subscription.format, MarketDataFormat::Json);
        assert_eq!(subscription.stream_type, "bookTicker");

        let url = subscription.to_websocket_url("wss://stream.binance.com:9443/stream");
        assert!(url.contains("btcusdt@bookTicker"));

        let message = subscription.to_subscribe_message();
        assert!(message.contains("btcusdt@bookTicker"));
    }

    #[test]
    fn test_auto_detect_parser() {
        let json_data = r#"{"s":"BTCUSDT","b":"50000.0","B":"1.0","a":"50001.0","A":"2.0"}"#;
        let market_data = MarketDataParser::parse_auto_detect(json_data.as_bytes()).unwrap();

        assert!(matches!(market_data, MarketData::BookTicker(_)));
        assert_eq!(market_data.symbol(), Some("BTCUSDT"));
    }
}
