use memchr;
use std::str;

#[derive(Debug)]
pub struct BookTicker<'a> {
    pub symbol: &'a str,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
}

pub fn parse_bookticker(input: &[u8]) -> Option<BookTicker> {
    // 1. 查找 "s":"BTCUSDT" 的位置
    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];
    // 2. 查找 "b":0.00000000 的位置
    let input = &input[start + end..];
    let bid_price_pattern = b"\"b\":\"";
    let start = memchr::memmem::find(input, bid_price_pattern)?;
    let start = start + bid_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid_price = &input[start..start + end];
    // 4. 查找 "a":0.00000000 的位置
    let input = &input[start + end..];
    let ask_price_pattern = b"\"a\":\"";
    let start = memchr::memmem::find(input, ask_price_pattern)?;
    let start = start + ask_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask_price = &input[start..start + end];

    let symbol = unsafe { std::mem::transmute::<&[u8], &str>(symbol) };
    let bid_price: &str = unsafe { std::mem::transmute(bid_price) };
    let ask_price: &str = unsafe { std::mem::transmute(ask_price) };
    Some(BookTicker {
        symbol,
        bid_price: bid_price.parse::<f64>().unwrap(),
        bid_qty: 0.0,
        ask_price: ask_price.parse::<f64>().unwrap(),
        ask_qty: 0.0,
    })
}

/// SIMD优化的JSON BookTicker解析器（占位符实现）
/// 注意：这是一个占位符实现，实际的SIMD优化在SBE模块中
pub fn parse_bookticker_simd(input: &[u8]) -> Option<BookTicker> {
    // 简单地调用常规解析器
    parse_bookticker(input)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_bookticker() {
        let input = r#"{"s":"BTCUSDT","b":"0.00000000","B":"0.00000000","a":"0.00000000","A":"0.00000000"}"#;
        let bookticker = parse_bookticker(input.as_bytes()).unwrap();
        assert_eq!(bookticker.symbol, "BTCUSDT");
        assert_eq!(bookticker.bid_price, 0.0);
        assert_eq!(bookticker.bid_qty, 0.0);
        assert_eq!(bookticker.ask_price, 0.0);
        assert_eq!(bookticker.ask_qty, 0.0);

        let input = r#"{"s":"BTCUSDT","b":"2342423.000000","B":"0.00000000001","a":"123.00000000","A":"234.00000000"}"#;
        let bookticker = parse_bookticker(input.as_bytes()).unwrap();
        assert_eq!(bookticker.symbol, "BTCUSDT");
        assert_eq!(bookticker.bid_price, 2342423.0);
        assert_eq!(bookticker.bid_qty, 0.0);
        assert_eq!(bookticker.ask_price, 123.0);
        assert_eq!(bookticker.ask_qty, 0.0);
    }
}
