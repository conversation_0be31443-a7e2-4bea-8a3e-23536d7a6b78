// benches/parse_bench.rs
use criterion::{Criterion, criterion_group, criterion_main};
use libwebsocket_rs::encoding::book_ticker::{parse_bookticker, parse_bookticker_simd};
use std::hint::black_box;

fn bench(c: &mut Criterion) {
    let data = r#"{"s":"BTCUSDT","b":"123.390","B":"43290.2142","a":"123.400","A":"43290.2143"}"#;
    c.bench_function("parse_book_ticker", |b| {
        b.iter(|| {
            let _ = parse_bookticker(black_box(data.as_bytes()));
        })
    });
}

fn bench_simd(c: &mut Criterion) {
    let data = r#"{"s":"BTCUSDT","b":"123.390","B":"43290.2142","a":"123.400","A":"43290.2143"}"#
        .as_bytes();
    c.bench_function("parse_book_ticker_simd", |b| {
        b.iter(|| {
            let _ = parse_bookticker_simd(black_box(data));
        })
    });
}
criterion_group! {
    name = benches;
    config = Criterion::default().sample_size(1000);
    targets = bench, bench_simd
}
criterion_main!(benches);
