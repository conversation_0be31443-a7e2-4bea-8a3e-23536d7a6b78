use criterion::{BenchmarkId, Criterion, criterion_group, criterion_main};
use libwebsocket_rs::encoding::{
    book_ticker::parse_bookticker,
    market_data::{MarketDataFormat, MarketDataParser},
    sbe::{
        book_ticker::{
            parse_sbe_bookticker, parse_sbe_bookticker_batch, parse_sbe_bookticker_batch_auto,
            parse_sbe_bookticker_simd_auto,
        },
        decoder::SbeDecoder,
        schema::{SbeHeader, SbeMessageType},
    },
};
use std::hint::black_box;

fn create_json_bookticker_data() -> Vec<u8> {
    r#"{"s":"BTCUSDT","b":"50000.12345","B":"1.23456789","a":"50001.54321","A":"2.87654321"}"#
        .as_bytes()
        .to_vec()
}

fn create_sbe_bookticker_data() -> Vec<u8> {
    let mut data = Vec::new();

    // SBE头部
    let header = SbeHeader {
        block_length: 56,
        template_id: 1001, // BookTicker
        schema_id: 1,
        version: 1,
    };

    data.extend_from_slice(&header.block_length.to_le_bytes());
    data.extend_from_slice(&header.template_id.to_le_bytes());
    data.extend_from_slice(&header.schema_id.to_le_bytes());
    data.extend_from_slice(&header.version.to_le_bytes());

    // 消息体
    let mut message_body = vec![0u8; 56];

    // Symbol: "BTCUSDT"
    let symbol_bytes = b"BTCUSDT\0\0\0\0\0\0\0\0\0";
    message_body[0..16].copy_from_slice(symbol_bytes);

    // Prices and quantities
    let bid_price = 50000.12345f64.to_le_bytes();
    let bid_qty = 1.23456789f64.to_le_bytes();
    let ask_price = 50001.54321f64.to_le_bytes();
    let ask_qty = 2.87654321f64.to_le_bytes();
    let timestamp = 1640995200000u64.to_le_bytes();

    message_body[16..24].copy_from_slice(&bid_price);
    message_body[24..32].copy_from_slice(&bid_qty);
    message_body[32..40].copy_from_slice(&ask_price);
    message_body[40..48].copy_from_slice(&ask_qty);
    message_body[48..56].copy_from_slice(&timestamp);

    data.extend_from_slice(&message_body);
    data
}

fn create_batch_json_data(count: usize) -> Vec<u8> {
    let single_json = create_json_bookticker_data();
    let mut batch_data = Vec::new();

    for i in 0..count {
        if i > 0 {
            batch_data.push(b'\n');
        }
        batch_data.extend_from_slice(&single_json);
    }

    batch_data
}

fn create_batch_sbe_data(count: usize) -> Vec<u8> {
    let single_sbe = create_sbe_bookticker_data();
    let mut batch_data = Vec::new();

    for _ in 0..count {
        batch_data.extend_from_slice(&single_sbe);
    }

    batch_data
}

fn bench_json_parsing(c: &mut Criterion) {
    let json_data = create_json_bookticker_data();

    c.bench_function("json_parse_bookticker_direct", |b| {
        b.iter(|| {
            let _ = parse_bookticker(black_box(&json_data));
        })
    });

    let json_parser = MarketDataParser::new(MarketDataFormat::Json);
    c.bench_function("json_parse_market_data_parser", |b| {
        b.iter(|| {
            let _ = json_parser.parse(black_box(&json_data));
        })
    });
}

fn bench_sbe_parsing(c: &mut Criterion) {
    let sbe_data = create_sbe_bookticker_data();

    // 测试直接SBE解析
    c.bench_function("sbe_parse_bookticker_direct", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker(black_box(&sbe_data[8..])); // 跳过头部
        })
    });

    // 测试SIMD优化的SBE解析
    c.bench_function("sbe_parse_bookticker_simd_auto", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker_simd_auto(black_box(&sbe_data[8..])); // 跳过头部
        })
    });

    // 测试完整SBE解码器
    c.bench_function("sbe_decode_complete", |b| {
        b.iter(|| {
            let _ = SbeDecoder::decode(black_box(&sbe_data));
        })
    });

    // 测试市场数据解析器
    let sbe_parser = MarketDataParser::new(MarketDataFormat::Sbe);
    c.bench_function("sbe_parse_market_data_parser", |b| {
        b.iter(|| {
            let _ = sbe_parser.parse(black_box(&sbe_data));
        })
    });
}

fn bench_auto_detect_parsing(c: &mut Criterion) {
    let json_data = create_json_bookticker_data();
    let sbe_data = create_sbe_bookticker_data();

    c.bench_function("auto_detect_json", |b| {
        b.iter(|| {
            let _ = MarketDataParser::parse_auto_detect(black_box(&json_data));
        })
    });

    c.bench_function("auto_detect_sbe", |b| {
        b.iter(|| {
            let _ = MarketDataParser::parse_auto_detect(black_box(&sbe_data));
        })
    });
}

fn bench_batch_parsing(c: &mut Criterion) {
    let mut group = c.benchmark_group("batch_parsing");

    for count in [10, 100, 1000].iter() {
        let json_batch = create_batch_json_data(*count);
        let sbe_batch = create_batch_sbe_data(*count);

        let json_parser = MarketDataParser::new(MarketDataFormat::Json);
        let sbe_parser = MarketDataParser::new(MarketDataFormat::Sbe);

        group.bench_with_input(BenchmarkId::new("json_batch", count), count, |b, _| {
            b.iter(|| {
                let _ = json_parser.parse_batch(black_box(&json_batch));
            })
        });

        group.bench_with_input(BenchmarkId::new("sbe_batch", count), count, |b, _| {
            b.iter(|| {
                let _ = sbe_parser.parse_batch(black_box(&sbe_batch));
            })
        });

        group.bench_with_input(
            BenchmarkId::new("sbe_decoder_batch", count),
            count,
            |b, _| {
                b.iter(|| {
                    let _ = SbeDecoder::decode_batch(black_box(&sbe_batch));
                })
            },
        );

        // 测试直接SBE批量解析
        group.bench_with_input(
            BenchmarkId::new("sbe_batch_direct", count),
            count,
            |b, _| {
                b.iter(|| {
                    let _ = parse_sbe_bookticker_batch(black_box(&sbe_batch[8..])); // 跳过头部
                })
            },
        );

        // 测试SIMD优化的SBE批量解析
        group.bench_with_input(
            BenchmarkId::new("sbe_batch_simd_auto", count),
            count,
            |b, _| {
                b.iter(|| {
                    let _ = parse_sbe_bookticker_batch_auto(black_box(&sbe_batch[8..])); // 跳过头部
                })
            },
        );
    }

    group.finish();
}

fn bench_message_size_comparison(c: &mut Criterion) {
    let json_data = create_json_bookticker_data();
    let sbe_data = create_sbe_bookticker_data();

    println!("Message size comparison:");
    println!("JSON: {} bytes", json_data.len());
    println!("SBE:  {} bytes", sbe_data.len());
    println!(
        "SBE is {:.1}% smaller",
        (1.0 - sbe_data.len() as f64 / json_data.len() as f64) * 100.0
    );

    // 基准测试消息大小对性能的影响
    c.bench_function("json_memory_copy", |b| {
        b.iter(|| {
            let _ = black_box(json_data.clone());
        })
    });

    c.bench_function("sbe_memory_copy", |b| {
        b.iter(|| {
            let _ = black_box(sbe_data.clone());
        })
    });
}

fn bench_format_validation(c: &mut Criterion) {
    let json_data = create_json_bookticker_data();
    let sbe_data = create_sbe_bookticker_data();
    let invalid_data = vec![0u8; 32];

    c.bench_function("sbe_format_validation_valid", |b| {
        b.iter(|| {
            let _ = SbeDecoder::is_sbe_message(black_box(&sbe_data));
        })
    });

    c.bench_function("sbe_format_validation_invalid", |b| {
        b.iter(|| {
            let _ = SbeDecoder::is_sbe_message(black_box(&invalid_data));
        })
    });

    c.bench_function("sbe_format_validation_json", |b| {
        b.iter(|| {
            let _ = SbeDecoder::is_sbe_message(black_box(&json_data));
        })
    });
}

fn bench_conversion_overhead(c: &mut Criterion) {
    let sbe_data = create_sbe_bookticker_data();
    let sbe_parser = MarketDataParser::new(MarketDataFormat::Sbe);

    c.bench_function("sbe_to_standard_conversion", |b| {
        b.iter(|| {
            if let Some(market_data) = sbe_parser.parse(black_box(&sbe_data)) {
                let _ = market_data.as_book_ticker();
            }
        })
    });
}

fn bench_architecture_optimizations(c: &mut Criterion) {
    let sbe_data = create_sbe_bookticker_data();
    let mut group = c.benchmark_group("architecture_optimizations");

    // 基准测试：常规解析
    group.bench_function("regular_parsing", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker(black_box(&sbe_data[8..]));
        })
    });

    // SIMD优化解析（自动选择架构）
    group.bench_function("simd_auto_parsing", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker_simd_auto(black_box(&sbe_data[8..]));
        })
    });

    // 批量处理对比
    let batch_data = create_batch_sbe_data(100);

    group.bench_function("regular_batch_100", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker_batch(black_box(&batch_data[8..]));
        })
    });

    group.bench_function("simd_auto_batch_100", |b| {
        b.iter(|| {
            let _ = parse_sbe_bookticker_batch_auto(black_box(&batch_data[8..]));
        })
    });

    group.finish();
}

criterion_group!(
    benches,
    bench_json_parsing,
    bench_sbe_parsing,
    bench_auto_detect_parsing,
    bench_batch_parsing,
    bench_message_size_comparison,
    bench_format_validation,
    bench_conversion_overhead,
    bench_architecture_optimizations
);

criterion_main!(benches);
