// benches/parse_bench.rs
use criterion::{Criterion, criterion_group, criterion_main};
use libwebsocket_rs::encoding::order_update::parse_order_update;
use std::hint::black_box;

// 读取一个典型的样本 JSON（用真实的用户数据流样本）
fn load_sample() -> (&'static str, &'static str) {
    let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "P": "0.00000000",
    "F": "0.00000000",
    "g": -1,
    "C":"",
    "x":"TRADE",
    "X":"TRADE",
    "r": "NONE",
    "i": 43735702642,
    "l": "0.00000000",
    "z": "0.00000000",
    "L":"103443.430",
    "n": "0",
    "N": null,
    "T": 1748250466926,
    "t": -1,
    "I": 93070863163,
    "w": false,
    "m": false,
    "M": false,
    "O": 1748250466926,
    "Z": "0.00000000",
    "Y": "0.00000000",
    "Q": "0.00000000",
    "W": 1748250466926,
    "V": "EXPIRE_MAKER"
  }
}

        "#;
    let data1 = r#"
{
  "id": "341935215578177",
  "status": 200,
  "result": {
    "symbol":"BTCUSDT",
    "orderId": 43735702641,
    "orderListId": -1,
    "clientOrderId":"341935215578177-0-0",
    "transactTime": 1748250466926,
    "price": "109624.00000000",
    "origQty": "0.00011000",
    "executedQty": "0.00000000",
    "origQuoteOrderQty": "0.00000000",
    "cummulativeQuoteQty": "0.00000000",
    "status": "EXPIRED",
    "timeInForce": "IOC",
    "type": "LIMIT",
    "side": "BUY",
    "workingTime": 1748250466926,
    "fills": [],
    "selfTradePreventionMode": "EXPIRE_MAKER"
  },
  "rateLimits": [
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 100,
      "count": 90
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "DAY",
      "intervalNum": 1,
      "limit": 200000,
      "count": 40240
    },
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 6000,
      "count": 141
    }
  ]
}

        "#;
    (data, data1)
}

fn bench_old(c: &mut Criterion) {
    let data = load_sample();
    let mut i = 0;
    c.bench_function("parse_order_update_old", |b| {
        b.iter(|| {
            // 黑盒处理，防止编译层面优化掉
            i += 1;
            let _ = parse_order_update(black_box(if i % 2 == 0 {
                data.0.as_bytes()
            } else {
                data.1.as_bytes()
            }));
        })
    });
}

fn bench_new(c: &mut Criterion) {
    let data = load_sample();
    let mut i = 0;
    c.bench_function("parse_order_update_optimized", |b| {
        b.iter(|| {
            i += 1;
            let _ = parse_order_update(black_box(if i % 2 == 0 {
                data.0.as_bytes()
            } else {
                data.1.as_bytes()
            }));
        })
    });
}

criterion_group! {
    name = benches;
    config = Criterion::default().sample_size(400);
    targets = bench_old, bench_new
}
criterion_main!(benches);
