use libwebsocket_rs::engine::latency_measurement::{resolve_domain_ips, resolve_domain_ips_multiple, test_domain_resolution};

fn main() {
    println!("域名解析工具 - Binance WebSocket域名IP查询\n");
    
    // 运行内置的测试函数
    test_domain_resolution();
    
    println!("\n=== 详细的Binance域名解析 ===");
    
    // 详细测试主要的Binance WebSocket域名
    let domains_to_test = vec![
        ("stream.binance.com", "现货WebSocket"),
        ("fstream.binance.com", "期货WebSocket"), 
        ("dstream.binance.com", "交割合约WebSocket"),
        ("ws-api.binance.com", "WebSocket API"),
    ];
    
    for (domain, description) in domains_to_test {
        println!("\n--- {} ({}) ---", domain, description);
        
        // 单次解析
        println!("单次解析:");
        match resolve_domain_ips(domain, Some(443)) {
            Ok(ips) => {
                println!("  找到 {} 个IP地址:", ips.len());
                for (i, ip) in ips.iter().enumerate() {
                    println!("    {}. {} (端口: {})", i + 1, ip.ip(), ip.port());
                }
            }
            Err(e) => {
                println!("  解析失败: {}", e);
            }
        }
        
        // 多次解析获取更多IP
        println!("多次解析 (5次尝试):");
        match resolve_domain_ips_multiple(domain, Some(443), Some(5), Some(300)) {
            Ok(ips) => {
                println!("  总共找到 {} 个唯一IP地址:", ips.len());
                for (i, ip) in ips.iter().enumerate() {
                    println!("    {}. {}", i + 1, ip.ip());
                }
            }
            Err(e) => {
                println!("  多次解析失败: {}", e);
            }
        }
    }
    
    println!("\n=== 解析完成 ===");
    println!("提示: 不同的IP地址可能对应不同的CDN节点，选择延迟最低的IP可以提高连接性能。");
}
