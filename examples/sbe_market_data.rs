use std::{
    net::{IpAddr, Ipv4Addr, SocketAddr},
    str::FromStr,
};

use libwebsocket_rs::{
    CallbackData, Message, Result, Settings, Token, WebSocket, WebSocketHandle,
    encoding::market_data::{MarketDataParser, MarketDataFormat, MarketDataSubscription},
    net::utils::url::Url,
};

const MARKET_DATA_TOKEN: Token = Token(0);

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // 设置高性能参数
    let mut settings = Settings::default();
    settings.tcp_nodelay = true;
    settings.max_connections = 10;
    
    // 创建SBE格式的行情解析器
    let sbe_parser = MarketDataParser::new(MarketDataFormat::Sbe);
    let json_parser = MarketDataParser::new(MarketDataFormat::Json);
    
    const N: usize = 1024 * 32;
    let callback = move |handle: &mut WebSocketHandle<N>, cd: CallbackData| -> Result<()> {
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(text) => {
                    match token {
                        MARKET_DATA_TOKEN => {
                            // 尝试自动检测格式并解析
                            if let Some(market_data) = MarketDataParser::parse_auto_detect(text.as_ref()) {
                                if let Some(book_ticker) = market_data.as_book_ticker() {
                                    println!(
                                        "JSON BookTicker: {} - Bid: {:.2} @ {:.6}, Ask: {:.2} @ {:.6}",
                                        book_ticker.symbol,
                                        book_ticker.bid_price,
                                        book_ticker.bid_qty,
                                        book_ticker.ask_price,
                                        book_ticker.ask_qty
                                    );
                                }
                            }
                        }
                        _ => {}
                    }
                }
                Message::WebsocketSbeBinary(binary_data) => {
                    match token {
                        MARKET_DATA_TOKEN => {
                            // 解析SBE格式的行情数据
                            if let Some(market_data) = sbe_parser.parse(binary_data.as_ref()) {
                                if let Some(book_ticker) = market_data.as_book_ticker() {
                                    println!(
                                        "SBE BookTicker: {} - Bid: {:.2} @ {:.6}, Ask: {:.2} @ {:.6}",
                                        book_ticker.symbol,
                                        book_ticker.bid_price,
                                        book_ticker.bid_qty,
                                        book_ticker.ask_price,
                                        book_ticker.ask_qty
                                    );
                                }
                            }
                        }
                        _ => {}
                    }
                }
                _ => {}
            },
            CallbackData::ConnectionOpen(token) => {
                match token {
                    MARKET_DATA_TOKEN => {
                        println!("Market data connection opened");
                        
                        // 订阅JSON格式的BookTicker
                        let json_subscription = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Json);
                        let subscribe_msg = json_subscription.to_subscribe_message();
                        handle.send_message(token, subscribe_msg)?;
                        
                        // 如果支持SBE格式，也可以订阅SBE格式
                        // let sbe_subscription = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Sbe);
                        // let sbe_subscribe_msg = sbe_subscription.to_subscribe_message();
                        // handle.send_message(token, sbe_subscribe_msg)?;
                    }
                    _ => {}
                }
            }
            CallbackData::ConnectionClose(token) => {
                println!("Connection closed: {:?}", token);
            }
            CallbackData::ConnectionError(token, error) => {
                println!("Connection error: {:?} - {:?}", token, error);
            }
        }
        Ok(())
    };
    
    let mut websocket = WebSocket::new(settings, callback)?;
    
    // 连接到Binance WebSocket流
    let market_data_url = "wss://stream.binance.com:9443/ws/btcusdt@bookTicker";
    websocket.connect(market_data_url, MARKET_DATA_TOKEN)?;
    
    println!("Connected to market data stream. Press Ctrl+C to exit.");
    println!("Listening for both JSON and SBE format market data...");
    
    // 运行事件循环
    websocket.run()?;
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use libwebsocket_rs::encoding::sbe::{
        schema::{SbeHeader, SbeMessageType},
        book_ticker::parse_sbe_bookticker,
    };

    #[test]
    fn test_sbe_market_data_parsing() {
        // 创建一个模拟的SBE BookTicker消息
        let mut sbe_data = Vec::new();
        
        // SBE头部
        let header = SbeHeader {
            block_length: 56,
            template_id: 1001, // BookTicker
            schema_id: 1,
            version: 1,
        };
        
        // 将头部转换为字节（这里简化处理）
        sbe_data.extend_from_slice(&header.block_length.to_le_bytes());
        sbe_data.extend_from_slice(&header.template_id.to_le_bytes());
        sbe_data.extend_from_slice(&header.schema_id.to_le_bytes());
        sbe_data.extend_from_slice(&header.version.to_le_bytes());
        
        // 消息体（56字节）
        let mut message_body = vec![0u8; 56];
        
        // Symbol: "BTCUSDT"
        let symbol_bytes = b"BTCUSDT\0\0\0\0\0\0\0\0\0";
        message_body[0..16].copy_from_slice(symbol_bytes);
        
        // Prices and quantities
        let bid_price = 50000.0f64.to_le_bytes();
        let bid_qty = 1.5f64.to_le_bytes();
        let ask_price = 50001.0f64.to_le_bytes();
        let ask_qty = 2.0f64.to_le_bytes();
        let timestamp = 1640995200000u64.to_le_bytes();
        
        message_body[16..24].copy_from_slice(&bid_price);
        message_body[24..32].copy_from_slice(&bid_qty);
        message_body[32..40].copy_from_slice(&ask_price);
        message_body[40..48].copy_from_slice(&ask_qty);
        message_body[48..56].copy_from_slice(&timestamp);
        
        sbe_data.extend_from_slice(&message_body);
        
        // 测试SBE解析器
        let parser = MarketDataParser::new(MarketDataFormat::Sbe);
        if let Some(market_data) = parser.parse(&sbe_data) {
            if let Some(book_ticker) = market_data.as_book_ticker() {
                assert_eq!(book_ticker.symbol, "BTCUSDT");
                assert_eq!(book_ticker.bid_price, 50000.0);
                assert_eq!(book_ticker.ask_price, 50001.0);
                println!("SBE parsing test passed!");
            }
        }
    }

    #[test]
    fn test_json_market_data_parsing() {
        let json_data = r#"{"s":"BTCUSDT","b":"50000.0","B":"1.5","a":"50001.0","A":"2.0"}"#;
        
        let parser = MarketDataParser::new(MarketDataFormat::Json);
        if let Some(market_data) = parser.parse(json_data.as_bytes()) {
            if let Some(book_ticker) = market_data.as_book_ticker() {
                assert_eq!(book_ticker.symbol, "BTCUSDT");
                assert_eq!(book_ticker.bid_price, 50000.0);
                assert_eq!(book_ticker.ask_price, 50001.0);
                println!("JSON parsing test passed!");
            }
        }
    }

    #[test]
    fn test_auto_detect_parsing() {
        // 测试JSON自动检测
        let json_data = r#"{"s":"BTCUSDT","b":"50000.0","B":"1.5","a":"50001.0","A":"2.0"}"#;
        if let Some(market_data) = MarketDataParser::parse_auto_detect(json_data.as_bytes()) {
            assert!(matches!(market_data, libwebsocket_rs::encoding::market_data::MarketData::BookTicker(_)));
            println!("Auto-detect JSON test passed!");
        }
    }

    #[test]
    fn test_subscription_generation() {
        let subscription = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Json);
        let url = subscription.to_websocket_url("wss://stream.binance.com:9443/stream");
        let message = subscription.to_subscribe_message();
        
        assert!(url.contains("btcusdt@bookTicker"));
        assert!(message.contains("btcusdt@bookTicker"));
        println!("Subscription generation test passed!");
    }
}
