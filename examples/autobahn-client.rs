// extern crate env_logger;
// extern crate http;
// extern crate libwebsocket;
// extern crate log;
// extern crate mio;
// extern crate url;

// use libwebsocket::{CloseCode, ConnectionConfig, Message, Settings, WebSocket};
// use std::convert::TryInto;
// use std::env;
// use std::io::Write;
// use std::{cell::RefCell, rc::Rc};

// use env_logger::{Builder, Env};
// use log::{error, info, trace};

// fn setup_log() {
//     let log_level = env::var("LOG_LEVEL").expect("VAR1 not set");
//     Builder::from_env(Env::default().default_filter_or(log_level))
//         .format(|buf, record| {
//             writeln!(
//                 buf,
//                 "{}:{} - {} - {}",
//                 record.file().unwrap_or("unknown"),
//                 record.line().unwrap_or(0),
//                 record.level(),
//                 record.args()
//             )
//         })
//         .init();
// }

// fn run_all_tests(total_count: i32, current_count: i32) {
//     let mut settings = Settings::default();
//     settings.event_loop_timeout = Some(std::time::Duration::from_secs(10));
//     let mut ws = WebSocket::new(settings);
//     let total_count = Rc::new(RefCell::new(total_count));
//     let current_count = Rc::new(RefCell::new(current_count));
//     loop {
//         let total_count_clone = total_count.clone();
//         let current_count_clone = current_count.clone();
//         let connection_config = {
//             let tc = total_count.as_ref().borrow_mut();
//             let cc = current_count.as_ref().borrow_mut();
//             if *tc == -1 {
//                 ConnectionConfig::WebSocket(
//                     url::Url::parse("ws://127.0.0.1:9001/getCaseCount").unwrap(),
//                 )
//             } else if *cc <= *tc {
//                 ConnectionConfig::WebSocket(
//                     url::Url::parse(
//                         format!(
//                             "ws://127.0.0.1:9001/runCase?case={}&agent=libwebsockets",
//                             *cc
//                         )
//                         .as_str(),
//                     )
//                     .unwrap(),
//                 )
//             } else if *cc == (*tc + 1) {
//                 ConnectionConfig::WebSocket(
//                     url::Url::parse("ws://127.0.0.1:9001/updateReports?agent=libwebsockets")
//                         .unwrap(),
//                 )
//             } else {
//                 break;
//             }
//         };
//         let handle = match ws.connect(connection_config) {
//             Ok(handle) => handle,
//             Err(err) => {
//                 error!("connect error: {:?}", err);
//                 break;
//             }
//         };
//         ws.set_on_message_cb(handle, move |msg| {
//             trace!("on_message_cb: {:?}", msg);
//             let mut tc = total_count_clone.as_ref().borrow_mut();
//             if *tc == -1 {
//                 *tc = match msg.try_into() {
//                     Ok(tc) => tc,
//                     Err(err) => {
//                         error!("try_into error: {:?}", err);
//                         return Ok(Some(vec![(
//                             handle,
//                             Message::Close(CloseCode::Normal, "done".into()),
//                         )]));
//                     }
//                 };
//             } else {
//                 return Ok(Some(vec![(handle, Message::new_from(msg))]));
//             }
//             Ok(None)
//         })
//         .unwrap();
//         ws.set_on_open_cb(handle, || {
//             trace!("on_open_cb");
//             Ok(None)
//         })
//         .unwrap();
//         ws.set_on_error_cb(handle, |err| {
//             trace!("on_error_cb: {:?}", err);
//         })
//         .unwrap();
//         ws.set_on_close_cb(handle, move |err, msg| {
//             trace!("on_close_cb: {:?} {:?}", err, msg);
//             let mut cc = current_count_clone.as_ref().borrow_mut();
//             info!("done test: {:?}", *cc);
//             *cc += 1;
//         })
//         .unwrap();
//         ws.run(false).unwrap();
//     }
// }

// fn main() {
//     setup_log();
//     let total_count = env::var("TOTAL_COUNT").expect("VAR1 not set");
//     let current_count = env::var("CURRENT_COUNT").expect("VAR2 not set");

//     let tc: i32 = total_count.parse().expect("VAR1 is not a valid i32");
//     let cc: i32 = current_count.parse().expect("VAR2 is not a valid i32");
//     run_all_tests(tc, cc);
// }

fn main() {}
