use std::time::{SystemTime, UNIX_EPOCH};

use hmac::{Hmac, Mac};
use libwebsocket_rs::{CallbackData, Message, Result, Settings, WebSocket, WebSocketHandle};
use mio::Token;
use sha2::Sha256;

type HmacSha256 = Hmac<Sha256>;

const TOKEN: Token = Token(0);
// Note: In a real application, you would import these from the generated trading_pair module:
// use libwebsocket_rs::engine::trading_pair::{EXAMPLE_API_KEY, EXAMPLE_SECRET_KEY};
const API_KEY: &str = "example_api_key_placeholder";
const SECRET_KEY: &str = "example_secret_key_placeholder";

fn get_timestamp() -> u64 {
    let start = SystemTime::now();
    let since_epoch = start.duration_since(UNIX_EPOCH).unwrap();
    since_epoch.as_millis() as u64
}

fn sign(query: &str, secret_key: &str) -> String {
    let mut mac =
        HmacSha256::new_from_slice(secret_key.as_bytes()).expect("HMAC can take key of any size");
    mac.update(query.as_bytes());
    hex::encode(mac.finalize().into_bytes())
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let settings = Settings::default();
    let callback = |_handle: &mut WebSocketHandle<1024>, cd: CallbackData| -> Result<()> {
        match cd {
            CallbackData::Message(_, msg) => match msg {
                Message::HttpResponse(text) => {}
                _ => (),
            },
            CallbackData::ConnectionOpen(_) => {
                println!("got connection open");
                _handle.stop();
            }
            _ => println!("got callback: {:?}", cd),
        }
        Ok(())
    };
    let mut websocket = WebSocket::new(settings, callback)?;
    websocket.connect("https://api.binance.com", TOKEN)?;
    websocket.run().unwrap();
    Ok(())
}
