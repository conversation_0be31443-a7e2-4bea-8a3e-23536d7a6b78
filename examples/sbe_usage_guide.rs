/// SBE使用指南示例
/// 
/// 本示例展示了如何在实际项目中使用新的SBE支持功能
/// 包括：
/// 1. 基本的SBE消息解析
/// 2. 自动格式检测
/// 3. WebSocket集成
/// 4. 性能优化技巧

use std::time::Instant;
use libwebsocket_rs::encoding::{
    market_data::{MarketDataParser, MarketDataFormat, MarketDataSubscription, MarketData},
    sbe::{
        schema::{SbeHeader, SbeMessageType},
        decoder::SbeDecoder,
        book_ticker::parse_sbe_bookticker,
    },
    book_ticker::parse_bookticker,
};

fn main() {
    println!("=== SBE使用指南示例 ===\n");
    
    // 1. 基本SBE解析示例
    basic_sbe_parsing_example();
    
    // 2. 自动格式检测示例
    auto_format_detection_example();
    
    // 3. 批量处理示例
    batch_processing_example();
    
    // 4. 性能对比示例
    performance_comparison_example();
    
    // 5. 订阅配置示例
    subscription_configuration_example();
}

/// 1. 基本SBE解析示例
fn basic_sbe_parsing_example() {
    println!("1. 基本SBE解析示例");
    println!("==================");
    
    // 创建模拟的SBE数据
    let sbe_data = create_sample_sbe_data();
    
    // 方法1: 使用SBE解码器
    if let Some(sbe_message) = SbeDecoder::decode(&sbe_data) {
        match sbe_message {
            libwebsocket_rs::encoding::sbe::decoder::SbeMessage::BookTicker(ticker) => {
                println!("✓ SBE解码器解析成功:");
                println!("  Symbol: {}", ticker.symbol);
                println!("  Bid: {:.2} @ {:.6}", ticker.bid_price, ticker.bid_qty);
                println!("  Ask: {:.2} @ {:.6}", ticker.ask_price, ticker.ask_qty);
            }
            _ => println!("✗ 非BookTicker消息"),
        }
    }
    
    // 方法2: 使用市场数据解析器
    let parser = MarketDataParser::new(MarketDataFormat::Sbe);
    if let Some(market_data) = parser.parse(&sbe_data) {
        if let Some(book_ticker) = market_data.as_book_ticker() {
            println!("✓ 市场数据解析器解析成功:");
            println!("  Symbol: {}", book_ticker.symbol);
            println!("  Spread: {:.2}", book_ticker.ask_price - book_ticker.bid_price);
        }
    }
    
    println!();
}

/// 2. 自动格式检测示例
fn auto_format_detection_example() {
    println!("2. 自动格式检测示例");
    println!("==================");
    
    // JSON格式数据
    let json_data = r#"{"s":"ETHUSDT","b":"3000.50","B":"10.0","a":"3001.00","A":"15.0"}"#;
    
    // SBE格式数据
    let sbe_data = create_sample_sbe_data();
    
    // 自动检测JSON格式
    if let Some(market_data) = MarketDataParser::parse_auto_detect(json_data.as_bytes()) {
        println!("✓ 自动检测到JSON格式:");
        if let Some(ticker) = market_data.as_book_ticker() {
            println!("  Symbol: {}", ticker.symbol);
            println!("  Format: JSON");
        }
    }
    
    // 自动检测SBE格式
    if let Some(market_data) = MarketDataParser::parse_auto_detect(&sbe_data) {
        println!("✓ 自动检测到SBE格式:");
        if let Some(ticker) = market_data.as_book_ticker() {
            println!("  Symbol: {}", ticker.symbol);
            println!("  Format: SBE");
        }
    }
    
    println!();
}

/// 3. 批量处理示例
fn batch_processing_example() {
    println!("3. 批量处理示例");
    println!("===============");
    
    // 创建批量JSON数据
    let json_batch = create_batch_json_data(5);
    let json_parser = MarketDataParser::new(MarketDataFormat::Json);
    let json_results = json_parser.parse_batch(&json_batch);
    
    println!("✓ JSON批量解析结果: {} 条消息", json_results.len());
    for (i, market_data) in json_results.iter().enumerate() {
        if let Some(ticker) = market_data.as_book_ticker() {
            println!("  [{}] {}: {:.2}", i + 1, ticker.symbol, ticker.bid_price);
        }
    }
    
    // 创建批量SBE数据
    let sbe_batch = create_batch_sbe_data(3);
    let sbe_parser = MarketDataParser::new(MarketDataFormat::Sbe);
    let sbe_results = sbe_parser.parse_batch(&sbe_batch);
    
    println!("✓ SBE批量解析结果: {} 条消息", sbe_results.len());
    for (i, market_data) in sbe_results.iter().enumerate() {
        if let Some(ticker) = market_data.as_book_ticker() {
            println!("  [{}] {}: {:.2}", i + 1, ticker.symbol, ticker.bid_price);
        }
    }
    
    println!();
}

/// 4. 性能对比示例
fn performance_comparison_example() {
    println!("4. 性能对比示例");
    println!("===============");
    
    let json_data = r#"{"s":"BTCUSDT","b":"50000.12","B":"1.5","a":"50001.34","A":"2.0"}"#;
    let sbe_data = create_sample_sbe_data();
    
    let iterations = 100_000;
    
    // JSON解析性能测试
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = parse_bookticker(json_data.as_bytes());
    }
    let json_duration = start.elapsed();
    
    // SBE解析性能测试
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = SbeDecoder::decode(&sbe_data);
    }
    let sbe_duration = start.elapsed();
    
    println!("✓ 性能对比结果 ({} 次迭代):", iterations);
    println!("  JSON解析: {:?}", json_duration);
    println!("  SBE解析:  {:?}", sbe_duration);
    println!("  性能提升: {:.1}x", 
             json_duration.as_nanos() as f64 / sbe_duration.as_nanos() as f64);
    
    // 消息大小对比
    println!("✓ 消息大小对比:");
    println!("  JSON: {} bytes", json_data.len());
    println!("  SBE:  {} bytes", sbe_data.len());
    println!("  空间节省: {:.1}%", 
             (1.0 - sbe_data.len() as f64 / json_data.len() as f64) * 100.0);
    
    println!();
}

/// 5. 订阅配置示例
fn subscription_configuration_example() {
    println!("5. 订阅配置示例");
    println!("===============");
    
    // JSON格式订阅
    let json_sub = MarketDataSubscription::book_ticker("BTCUSDT", MarketDataFormat::Json);
    println!("✓ JSON订阅配置:");
    println!("  URL: {}", json_sub.to_websocket_url("wss://stream.binance.com:9443/stream"));
    println!("  消息: {}", json_sub.to_subscribe_message());
    
    // SBE格式订阅
    let sbe_sub = MarketDataSubscription::book_ticker("ETHUSDT", MarketDataFormat::Sbe);
    println!("✓ SBE订阅配置:");
    println!("  URL: {}", sbe_sub.to_websocket_url("wss://stream.binance.com:9443/stream"));
    println!("  消息: {}", sbe_sub.to_subscribe_message());
    
    println!();
}

// 辅助函数：创建示例SBE数据
fn create_sample_sbe_data() -> Vec<u8> {
    let mut data = Vec::new();
    
    // SBE头部
    let header_bytes = [
        56, 0,    // block_length = 56
        233, 3,   // template_id = 1001 (BookTicker)
        1, 0,     // schema_id = 1
        1, 0,     // version = 1
    ];
    data.extend_from_slice(&header_bytes);
    
    // 消息体
    let mut message_body = vec![0u8; 56];
    
    // Symbol: "BTCUSDT"
    let symbol_bytes = b"BTCUSDT\0\0\0\0\0\0\0\0\0";
    message_body[0..16].copy_from_slice(symbol_bytes);
    
    // Prices and quantities
    let bid_price = 50000.12f64.to_le_bytes();
    let bid_qty = 1.5f64.to_le_bytes();
    let ask_price = 50001.34f64.to_le_bytes();
    let ask_qty = 2.0f64.to_le_bytes();
    let timestamp = 1640995200000u64.to_le_bytes();
    
    message_body[16..24].copy_from_slice(&bid_price);
    message_body[24..32].copy_from_slice(&bid_qty);
    message_body[32..40].copy_from_slice(&ask_price);
    message_body[40..48].copy_from_slice(&ask_qty);
    message_body[48..56].copy_from_slice(&timestamp);
    
    data.extend_from_slice(&message_body);
    data
}

// 辅助函数：创建批量JSON数据
fn create_batch_json_data(count: usize) -> Vec<u8> {
    let symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "SOLUSDT"];
    let mut batch_data = Vec::new();
    
    for i in 0..count {
        if i > 0 {
            batch_data.push(b'\n');
        }
        let symbol = symbols[i % symbols.len()];
        let price = 1000.0 + i as f64 * 100.0;
        let json = format!(
            r#"{{"s":"{}","b":"{:.2}","B":"1.0","a":"{:.2}","A":"2.0"}}"#,
            symbol, price, price + 1.0
        );
        batch_data.extend_from_slice(json.as_bytes());
    }
    
    batch_data
}

// 辅助函数：创建批量SBE数据
fn create_batch_sbe_data(count: usize) -> Vec<u8> {
    let mut batch_data = Vec::new();
    
    for i in 0..count {
        let single_sbe = create_sample_sbe_data();
        batch_data.extend_from_slice(&single_sbe);
    }
    
    batch_data
}
