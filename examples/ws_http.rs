// extern crate env_logger;
// extern crate http;
// extern crate libwebsocket;
// extern crate log;
// extern crate mio;
// extern crate url;

// use libwebsocket::{ConnectionConfig, ConvertToRequest, Message, WebSocket};
// use std::str::FromStr;

// use env_logger::{Builder, Env};
// use log::info;
// use url::Url;

// fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
//     Builder::from_env(Env::default().default_filter_or("Info"))
//         .format(|buf, record| {
//             use std::io::Write;
//             writeln!(
//                 buf,
//                 "{}[{}:{}] - {}",
//                 record.level(),
//                 record.file().unwrap_or("unknown"),
//                 record.line().unwrap_or(0),
//                 record.args()
//             )
//         })
//         .init();

//     let settings = libwebsocket::Settings::default();
//     let mut websocket = WebSocket::new(settings);

//     let url = Url::from_str("https://aws.okx.com").unwrap();
//     let config = ConnectionConfig::Http(url);
//     let http_handle = websocket.connect(config).unwrap();
//     websocket.set_on_http_response_cb(http_handle, move |response| {
//         info!("on_response_callback, res: {:?}", response);
//         Ok(Some(vec![(
//             http_handle,
//             Message::HttpRequest(
//                 "/api/v5/public/instruments?instType=SWAP"
//                     .into_request()
//                     .unwrap(),
//             ),
//         )]))
//     })?;
//     websocket.send_message(
//         http_handle,
//         Message::HttpRequest(
//             "/api/v5/public/instruments?instType=SWAP"
//                 .into_request()
//                 .unwrap(),
//         ),
//     )?;

//     let url = Url::from_str("wss://wsaws.okx.com:8443/ws/v5/public").unwrap();
//     let config = ConnectionConfig::WebSocket(url);
//     let ws_handle = websocket.connect(config).unwrap();
//     websocket.set_on_open_cb(ws_handle, move || {
//         info!("On open callback: {:?}", ws_handle);
//         Ok(Some(vec![(
//             ws_handle,
//             Message::OwnedText(
//                 format!(
//                 "{{\"op\": \"subscribe\",\"args\": [{{\"channel\": \"{}\",\"instId\": \"{}\"}}]}}",
//                 "bbo-tbt", "BTC-USD-SWAP"
//             )
//                 .into_bytes(),
//             ),
//         )]))
//     })?;
//     websocket.set_on_message_cb(http_handle, move |msg| {
//         info!("Received message: {:?}", msg);
//         Ok(Some(vec![(
//             http_handle,
//             Message::HttpRequest(
//                 "/api/v5/public/instruments?instType=SWAP"
//                     .into_request()
//                     .unwrap(),
//             ),
//         )]))
//     })?;

//     websocket.run(false).unwrap();
//     Ok(())
// }

fn main() {}
