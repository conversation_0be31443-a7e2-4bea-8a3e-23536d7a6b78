/// ARM64性能优化演示程序
/// 
/// 这个程序演示了ARM64架构优化对SBE格式BBO数据解析的性能提升。
/// 它比较了常规解析和SIMD优化解析的性能差异。

use std::time::Instant;
use libwebsocket_rs::encoding::sbe::{
    schema::{SbeHeader, SbeMessageType},
    book_ticker::{
        parse_sbe_bookticker,
        parse_sbe_bookticker_simd_auto,
        parse_sbe_bookticker_batch,
        parse_sbe_bookticker_batch_auto,
    },
};

fn create_test_sbe_data() -> Vec<u8> {
    let mut data = vec![0u8; 64]; // 8字节头部 + 56字节消息体
    
    // SBE头部
    let header = SbeHeader {
        block_length: 56,
        template_id: 1001, // BookTicker
        schema_id: 1,
        version: 1,
    };
    
    // 写入头部（使用unsafe转换为字节）
    unsafe {
        let header_bytes = std::slice::from_raw_parts(
            &header as *const SbeHeader as *const u8,
            8,
        );
        data[0..8].copy_from_slice(header_bytes);
    }
    
    // Symbol: "BTCUSDT"
    let symbol_bytes = b"BTCUSDT\0\0\0\0\0\0\0\0\0";
    data[8..24].copy_from_slice(symbol_bytes);
    
    // Bid price: 50000.0
    let bid_price_bytes = 50000.0f64.to_le_bytes();
    data[24..32].copy_from_slice(&bid_price_bytes);
    
    // Bid qty: 1.5
    let bid_qty_bytes = 1.5f64.to_le_bytes();
    data[32..40].copy_from_slice(&bid_qty_bytes);
    
    // Ask price: 50001.0
    let ask_price_bytes = 50001.0f64.to_le_bytes();
    data[40..48].copy_from_slice(&ask_price_bytes);
    
    // Ask qty: 2.0
    let ask_qty_bytes = 2.0f64.to_le_bytes();
    data[48..56].copy_from_slice(&ask_qty_bytes);
    
    // Timestamp: 1640995200000
    let timestamp_bytes = 1640995200000u64.to_le_bytes();
    data[56..64].copy_from_slice(&timestamp_bytes);
    
    data
}

fn create_batch_sbe_data(count: usize) -> Vec<u8> {
    let single_data = create_test_sbe_data();
    let mut batch_data = Vec::new();
    
    for _ in 0..count {
        batch_data.extend_from_slice(&single_data);
    }
    
    batch_data
}

fn benchmark_single_parsing(iterations: usize) {
    let sbe_data = create_test_sbe_data();
    let message_data = &sbe_data[8..]; // 跳过头部
    
    println!("=== 单消息解析性能测试 ===");
    println!("测试数据: {} 字节SBE消息", sbe_data.len());
    println!("迭代次数: {}", iterations);
    
    // 测试常规解析
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = parse_sbe_bookticker(message_data);
    }
    let regular_duration = start.elapsed();
    
    // 测试SIMD优化解析
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = parse_sbe_bookticker_simd_auto(message_data);
    }
    let simd_duration = start.elapsed();
    
    println!("常规解析时间: {:?}", regular_duration);
    println!("SIMD优化时间: {:?}", simd_duration);
    
    if simd_duration.as_nanos() > 0 {
        let speedup = regular_duration.as_nanos() as f64 / simd_duration.as_nanos() as f64;
        println!("性能提升: {:.2}x", speedup);
    }
    
    let regular_ns_per_op = regular_duration.as_nanos() / iterations as u128;
    let simd_ns_per_op = simd_duration.as_nanos() / iterations as u128;
    
    println!("常规解析: {} ns/操作", regular_ns_per_op);
    println!("SIMD优化: {} ns/操作", simd_ns_per_op);
}

fn benchmark_batch_parsing(batch_size: usize, iterations: usize) {
    let batch_data = create_batch_sbe_data(batch_size);
    let message_data = &batch_data[8..]; // 跳过第一个头部
    
    println!("\n=== 批量解析性能测试 ===");
    println!("批量大小: {} 消息", batch_size);
    println!("总数据量: {} 字节", batch_data.len());
    println!("迭代次数: {}", iterations);
    
    // 测试常规批量解析
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = parse_sbe_bookticker_batch(message_data);
    }
    let regular_duration = start.elapsed();
    
    // 测试SIMD优化批量解析
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = parse_sbe_bookticker_batch_auto(message_data);
    }
    let simd_duration = start.elapsed();
    
    println!("常规批量解析时间: {:?}", regular_duration);
    println!("SIMD优化批量解析时间: {:?}", simd_duration);
    
    if simd_duration.as_nanos() > 0 {
        let speedup = regular_duration.as_nanos() as f64 / simd_duration.as_nanos() as f64;
        println!("批量处理性能提升: {:.2}x", speedup);
    }
    
    let regular_msgs_per_sec = (batch_size * iterations) as f64 / regular_duration.as_secs_f64();
    let simd_msgs_per_sec = (batch_size * iterations) as f64 / simd_duration.as_secs_f64();
    
    println!("常规批量处理: {:.0} 消息/秒", regular_msgs_per_sec);
    println!("SIMD优化批量处理: {:.0} 消息/秒", simd_msgs_per_sec);
}

fn verify_correctness() {
    println!("=== 正确性验证 ===");
    
    let sbe_data = create_test_sbe_data();
    let message_data = &sbe_data[8..];
    
    let regular_result = parse_sbe_bookticker(message_data).unwrap();
    let simd_result = parse_sbe_bookticker_simd_auto(message_data).unwrap();
    
    println!("常规解析结果:");
    println!("  Symbol: {}", regular_result.symbol);
    println!("  Bid: {}, Ask: {}", regular_result.bid_price, regular_result.ask_price);
    
    println!("SIMD优化解析结果:");
    println!("  Symbol: {}", simd_result.symbol);
    println!("  Bid: {}, Ask: {}", simd_result.bid_price, simd_result.ask_price);
    
    let results_match = regular_result.symbol == simd_result.symbol
        && regular_result.bid_price == simd_result.bid_price
        && regular_result.ask_price == simd_result.ask_price
        && regular_result.bid_qty == simd_result.bid_qty
        && regular_result.ask_qty == simd_result.ask_qty
        && regular_result.timestamp == simd_result.timestamp;
    
    if results_match {
        println!("✅ 解析结果一致，优化正确！");
    } else {
        println!("❌ 解析结果不一致，存在问题！");
    }
}

fn print_architecture_info() {
    println!("=== 架构信息 ===");
    
    #[cfg(target_arch = "x86_64")]
    println!("当前架构: x86_64 (使用SSE/AVX优化)");
    
    #[cfg(target_arch = "aarch64")]
    println!("当前架构: ARM64/AArch64 (使用NEON优化)");
    
    #[cfg(not(any(target_arch = "x86_64", target_arch = "aarch64")))]
    println!("当前架构: 其他 (使用通用实现)");
    
    println!("编译优化: {}", if cfg!(debug_assertions) { "Debug" } else { "Release" });
}

fn main() {
    println!("ARM64 SBE性能优化演示");
    println!("====================");
    
    print_architecture_info();
    verify_correctness();
    
    // 单消息解析性能测试
    benchmark_single_parsing(100_000);
    
    // 批量解析性能测试
    benchmark_batch_parsing(100, 1_000);
    benchmark_batch_parsing(1000, 100);
    
    println!("\n=== 总结 ===");
    println!("本演示展示了ARM64架构优化对SBE解析性能的提升。");
    println!("在ARM64平台上，NEON SIMD指令可以显著提高数据处理速度。");
    println!("在x86_64平台上，SSE/AVX指令提供类似的优化效果。");
    println!("优化后的代码在保持正确性的同时，显著提升了处理性能。");
}
