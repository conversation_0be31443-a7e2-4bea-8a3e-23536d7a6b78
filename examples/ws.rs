// extern crate env_logger;
// extern crate http;
// extern crate libwebsocket;
// extern crate log;
// extern crate mio;
// extern crate url;

// use libwebsocket::{ConnectionConfig, Message, WebSocket};
// use std::{str::FromStr, vec};

// use env_logger::{Builder, Env};
// use log::info;
// use url::Url;

// fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
//     Builder::from_env(Env::default().default_filter_or("Trace"))
//         .format(|buf, record| {
//             use std::io::Write;
//             writeln!(
//                 buf,
//                 "{}[{}:{}] - {}",
//                 record.level(),
//                 record.file().unwrap_or("unknown"),
//                 record.line().unwrap_or(0),
//                 record.args()
//             )
//         })
//         .init();

//     let mut settings = libwebsocket::Settings::default();
//     settings.event_loop_timeout = Some(std::time::Duration::from_secs(10));
//     let mut websocket = WebSocket::new(settings);
//     let url = Url::from_str("wss://wsaws.okx.com:8443/ws/v5/public").unwrap();
//     let config = ConnectionConfig::WebSocket(url);
//     let ws_token = websocket.connect(config).unwrap();
//     websocket.set_on_open_cb(ws_token, move || {
//         info!("On open callback: {:?}", ws_token);
//         Ok(Some(vec![(
//             ws_token,
//             Message::OwnedText(
//                 format!(
//                 "{{\"op\": \"subscribe\",\"args\": [{{\"channel\": \"{}\",\"instId\": \"{}\"}}]}}",
//                 "bbo-tbt", "BTC-USD-SWAP"
//             )
//                 .into_bytes(),
//             ),
//         )]))
//     })?;
//     websocket.set_on_message_cb(ws_token, move |msg| {
//         info!("Received message: {:?}", msg);
//         Ok(None)
//     })?;
//     websocket.run(false).unwrap();
//     Ok(())
// }

fn main() {}
