# Cross-compilation configuration for ARM64 targets

[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"
rustflags = [
    "-C",
    "target-cpu=generic",
    "-C",
    "target-feature=+neon",
    # Uncomment the line below for static linking
    # "-C", "target-feature=+crt-static",
]

[target.aarch64-unknown-linux-musl]
linker = "aarch64-linux-musl-gcc"
rustflags = [
    "-C",
    "target-cpu=generic",
    "-C",
    "target-feature=+neon",
    "-C",
    "link-self-contained=yes",
]

# For Apple Silicon (macOS ARM64)
[target.aarch64-apple-darwin]
rustflags = ["-C", "target-cpu=apple-m1"]

# Build configuration
[build]
# Uncomment the line below to set ARM64 as default target
# target = "aarch64-unknown-linux-gnu"

# Environment variables for cross-compilation
[env]
CC_aarch64_unknown_linux_gnu = "aarch64-linux-gnu-gcc"
CXX_aarch64_unknown_linux_gnu = "aarch64-linux-gnu-g++"
AR_aarch64_unknown_linux_gnu = "aarch64-linux-gnu-ar"
CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER = "aarch64-linux-gnu-gcc"

CC_aarch64_unknown_linux_musl = "aarch64-linux-musl-gcc"
CXX_aarch64_unknown_linux_musl = "aarch64-linux-musl-g++"
AR_aarch64_unknown_linux_musl = "aarch64-linux-musl-ar"
CARGO_TARGET_AARCH64_UNKNOWN_LINUX_MUSL_LINKER = "aarch64-linux-musl-gcc"
