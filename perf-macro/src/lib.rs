use proc_macro::TokenStream;
use quote::quote;
use syn::{ItemFn, parse_macro_input};

#[proc_macro_attribute]
pub fn measure(_attr: TokenStream, item: TokenStream) -> TokenStream {
    let mut input = parse_macro_input!(item as ItemFn);

    // 获取函数标识信息
    let fn_name = input.sig.ident.to_string();
    let fn_block = &input.block;

    // 生成新代码块
    let new_block = quote! {
        {
            let __measure_guard = crate::utils::perf::MeasureGuard::new(concat!(#fn_name, "_latency"));
            let __measure_result = #fn_block;
            __measure_result
        }
    };

    // 替换原始代码块
    input.block = syn::parse2(new_block).unwrap();

    TokenStream::from(quote! { #input })
}
