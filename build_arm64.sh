#!/bin/bash

# ARM64 Cross-compilation Build Script for libwebsocket-rs
# This script helps build ARM64 binaries on x86_64 systems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
TARGET="aarch64-unknown-linux-gnu"
BUILD_TYPE="release"
INSTALL_DEPS=false
VERBOSE=false

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --target TARGET     Target architecture (default: aarch64-unknown-linux-gnu)"
    echo "                          Available: aarch64-unknown-linux-gnu, aarch64-unknown-linux-musl"
    echo "  -d, --debug            Build in debug mode (default: release)"
    echo "  -i, --install-deps     Install cross-compilation dependencies"
    echo "  -v, --verbose          Verbose output"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Build release for aarch64-unknown-linux-gnu"
    echo "  $0 -d                                 # Build debug version"
    echo "  $0 -t aarch64-unknown-linux-musl     # Build for musl target"
    echo "  $0 -i                                 # Install dependencies and build"
}

# Function to install cross-compilation dependencies
install_dependencies() {
    print_info "Installing cross-compilation dependencies..."
    
    if command -v apt-get >/dev/null 2>&1; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
        
        if [[ "$TARGET" == *"musl"* ]]; then
            # For musl targets, we need musl-tools
            sudo apt-get install -y musl-tools
            
            # Download and install musl cross-compiler if not available
            if ! command -v aarch64-linux-musl-gcc >/dev/null 2>&1; then
                print_info "Installing musl cross-compiler..."
                wget -q https://musl.cc/aarch64-linux-musl-cross.tgz
                sudo tar -xzf aarch64-linux-musl-cross.tgz -C /opt/
                sudo ln -sf /opt/aarch64-linux-musl-cross/bin/* /usr/local/bin/
                rm aarch64-linux-musl-cross.tgz
            fi
        fi
    elif command -v yum >/dev/null 2>&1; then
        # CentOS/RHEL/Fedora
        sudo yum install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
    elif command -v pacman >/dev/null 2>&1; then
        # Arch Linux
        sudo pacman -S --noconfirm aarch64-linux-gnu-gcc
    else
        print_error "Unsupported package manager. Please install cross-compilation tools manually."
        exit 1
    fi
    
    # Install Rust target
    print_info "Adding Rust target: $TARGET"
    rustup target add "$TARGET"
    
    print_success "Dependencies installed successfully!"
}

# Function to check if dependencies are available
check_dependencies() {
    local missing_deps=()
    
    # Check if Rust target is installed
    if ! rustup target list --installed | grep -q "$TARGET"; then
        print_warning "Rust target $TARGET is not installed"
        missing_deps+=("rust-target")
    fi
    
    # Check for cross-compiler
    if [[ "$TARGET" == *"musl"* ]]; then
        if ! command -v aarch64-linux-musl-gcc >/dev/null 2>&1; then
            missing_deps+=("musl-gcc")
        fi
    else
        if ! command -v aarch64-linux-gnu-gcc >/dev/null 2>&1; then
            missing_deps+=("gnu-gcc")
        fi
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_info "Run with -i flag to install dependencies automatically"
        return 1
    fi
    
    return 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -d|--debug)
            BUILD_TYPE="debug"
            shift
            ;;
        -i|--install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate target
case "$TARGET" in
    aarch64-unknown-linux-gnu|aarch64-unknown-linux-musl|aarch64-apple-darwin)
        ;;
    *)
        print_error "Unsupported target: $TARGET"
        print_info "Supported targets: aarch64-unknown-linux-gnu, aarch64-unknown-linux-musl, aarch64-apple-darwin"
        exit 1
        ;;
esac

print_info "Building for target: $TARGET"
print_info "Build type: $BUILD_TYPE"

# Install dependencies if requested
if [ "$INSTALL_DEPS" = true ]; then
    install_dependencies
fi

# Check dependencies
if ! check_dependencies; then
    exit 1
fi

# Build command
CARGO_CMD="cargo build --target $TARGET"

if [ "$BUILD_TYPE" = "release" ]; then
    CARGO_CMD="$CARGO_CMD --release"
fi

if [ "$VERBOSE" = true ]; then
    CARGO_CMD="$CARGO_CMD --verbose"
fi

# Execute build
print_info "Executing: $CARGO_CMD"
if $CARGO_CMD; then
    print_success "Build completed successfully!"
    
    # Show output location
    if [ "$BUILD_TYPE" = "release" ]; then
        OUTPUT_DIR="target/$TARGET/release"
    else
        OUTPUT_DIR="target/$TARGET/debug"
    fi
    
    print_info "Binary location: $OUTPUT_DIR/"
    
    # List built binaries
    if [ -d "$OUTPUT_DIR" ]; then
        print_info "Built binaries:"
        find "$OUTPUT_DIR" -maxdepth 1 -type f -executable -printf "  %f\n" 2>/dev/null || \
        find "$OUTPUT_DIR" -maxdepth 1 -type f -perm +111 -exec basename {} \; 2>/dev/null | sed 's/^/  /'
    fi
else
    print_error "Build failed!"
    exit 1
fi
