# 🚀 ARB 自动化部署和管理系统

这是一个完整的 ARB 交易程序自动化部署和管理系统，整合了 IP 优化、进程管理和监控功能。

## ✨ 系统特性

- 🔍 **智能 DNS 解析**: 自动获取 Binance 服务器的所有可用 IP 地址
- ⏱️ **延迟优化**: 测试所有 IP 组合，找到最优的延迟配置
- 🤖 **自动更新**: 自动更新代码中的 IP 配置并重新编译
- 🔄 **进程管理**: 使用 tmux 保持程序运行，异常退出时自动重启
- 📊 **监控面板**: 提供完整的性能分析和状态监控
- 🛠️ **配置管理**: 统一的配置管理和健康检查

## 📁 系统组件

### 核心脚本
- **`keep_arb_running.sh`** - 主要的进程管理脚本（已存在）
- **`scripts/smart_deploy.sh`** - 智能部署脚本（整合 IP 优化和进程管理）
- **`scripts/deploy_arb.py`** - IP 优化部署脚本
- **`scripts/arb_config.sh`** - 配置管理和监控脚本

### 辅助脚本
- **`scripts/dns_resolve.py`** - DNS 解析脚本
- **`scripts/test_deploy.py`** - 功能测试脚本
- **`scripts/demo.sh`** - 系统演示脚本

## 🚀 快速开始

### 方式一：智能部署（推荐）
```bash
# 完整的智能部署和启动
./scripts/smart_deploy.sh start
```

### 方式二：使用现有的进程管理
```bash
# 仅启动（使用现有 IP 配置）
./keep_arb_running.sh start

# 或者先优化 IP 再启动
./scripts/smart_deploy.sh optimize
./keep_arb_running.sh restart
```

### 方式三：配置管理
```bash
# 查看系统状态
./scripts/arb_config.sh show-config

# 完整部署
./scripts/arb_config.sh full-deploy

# 快速启动
./scripts/arb_config.sh quick-start
```

## 📋 详细用法

### 智能部署脚本 (smart_deploy.sh)
```bash
# 完整部署并启动
./scripts/smart_deploy.sh start

# 重新部署并重启
./scripts/smart_deploy.sh restart

# 仅执行 IP 优化
./scripts/smart_deploy.sh optimize

# 仅部署（不启动）
./scripts/smart_deploy.sh deploy

# 强制重新部署
./scripts/smart_deploy.sh start --force

# 查看帮助
./scripts/smart_deploy.sh help
```

### 进程管理 (keep_arb_running.sh)
```bash
# 启动进程
./keep_arb_running.sh start

# 停止进程
./keep_arb_running.sh stop

# 重启进程
./keep_arb_running.sh restart

# 查看状态
./keep_arb_running.sh status

# 连接到会话
./keep_arb_running.sh attach

# 查看日志
./keep_arb_running.sh logs
```

### 配置管理 (arb_config.sh)
```bash
# 显示系统配置
./scripts/arb_config.sh show-config

# 显示 IP 配置
./scripts/arb_config.sh show-ips

# 健康检查
./scripts/arb_config.sh health-check

# 清理日志
./scripts/arb_config.sh cleanup

# 安装依赖
./scripts/arb_config.sh install-deps
```

## 🔧 单独使用各个组件

### DNS 解析
```bash
cd scripts
python3 dns_resolve.py
```
结果保存在 `scripts/dns_results.json`

### 延迟检测
```bash
# 先确保 dns_results.json 存在
cargo build --release --bin order_latency_detector
./target/release/order_latency_detector
```

### Python 部署脚本
```bash
python3 scripts/deploy_arb.py
# 或者指定项目根目录
python3 scripts/deploy_arb.py --project-root /path/to/project
```

### tmux 运行脚本
```bash
./scripts/run_arb_tmux.sh
```

## 📊 延迟测试说明

延迟检测器会测试所有可用的 IP 组合，并输出详细的性能报告：

- **市场数据延迟**: 基于 event_time 计算的行情延迟
- **订单延迟**: 从发送订单到收到响应的延迟
- **百分位数**: p10, p20, p50, p90, p99

系统会自动选择总体性能最优的 IP 组合（订单延迟权重更高）。

## 🔄 tmux 会话管理

### 查看运行状态
```bash
tmux list-sessions
```

### 附加到会话
```bash
tmux attach-session -t arb_trading
```

### 分离会话
在 tmux 会话中按 `Ctrl+B`，然后按 `D`

### 停止程序
```bash
tmux kill-session -t arb_trading
```

## 🛠️ 故障排除

### 1. DNS 解析失败
```bash
# 检查网络连接
ping *******

# 手动安装 DNS 库
pip3 install dnspython
```

### 2. 编译失败
```bash
# 清理并重新编译
cargo clean
cargo build --release
```

### 3. 延迟测试无结果
- 检查网络连接
- 确认 API 密钥有效
- 查看防火墙设置

### 4. tmux 会话问题
```bash
# 杀死所有 tmux 会话
tmux kill-server

# 重新创建会话
./scripts/run_arb_tmux.sh
```

## 📁 文件说明

- `deploy_arb.py`: 主要的 Python 部署脚本
- `auto_deploy_and_run.sh`: 完整的自动化脚本
- `run_arb_tmux.sh`: tmux 运行脚本
- `dns_resolve.py`: DNS 解析脚本
- `dns_results.json`: DNS 解析结果

## ⚠️ 注意事项

1. **权限要求**: 程序会尝试设置高优先级调度，需要 root 权限才能生效
2. **网络要求**: 需要能够访问 Binance API
3. **资源要求**: 延迟测试会消耗一定的网络带宽和 API 配额
4. **监控建议**: 建议定期检查程序运行状态和性能指标

## 🔄 定期更新

建议每天或在网络环境变化时重新运行部署脚本，以确保使用最优的 IP 地址：

```bash
# 每日定时任务示例（添加到 crontab）
0 8 * * * cd /path/to/libwebsocket-rs && ./scripts/auto_deploy_and_run.sh --deploy-only
```
