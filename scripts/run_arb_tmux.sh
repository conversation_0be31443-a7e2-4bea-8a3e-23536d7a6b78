#!/bin/bash

# tmux 脚本：保持 arb 程序运行，自动重启
# 使用方法: ./scripts/run_arb_tmux.sh

set -e

# 配置
SESSION_NAME="arb_trading"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
ARB_BINARY="$PROJECT_ROOT/target/release/arb"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 检查 tmux 是否安装
if ! command -v tmux &> /dev/null; then
    error "tmux 未安装，请先安装 tmux"
    echo "Ubuntu/Debian: sudo apt-get install tmux"
    echo "CentOS/RHEL: sudo yum install tmux"
    echo "macOS: brew install tmux"
    exit 1
fi

# 检查是否已经存在同名会话
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    warn "tmux 会话 '$SESSION_NAME' 已存在"
    echo "选择操作:"
    echo "1) 附加到现有会话"
    echo "2) 杀死现有会话并创建新会话"
    echo "3) 退出"
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            log "附加到现有会话..."
            tmux attach-session -t "$SESSION_NAME"
            exit 0
            ;;
        2)
            log "杀死现有会话..."
            tmux kill-session -t "$SESSION_NAME"
            ;;
        3)
            log "退出"
            exit 0
            ;;
        *)
            error "无效选择"
            exit 1
            ;;
    esac
fi

# 检查 arb 二进制文件是否存在
if [ ! -f "$ARB_BINARY" ]; then
    warn "arb 二进制文件不存在，正在编译..."
    cd "$PROJECT_ROOT"
    if ! cargo build --release --bin arb; then
        error "编译 arb 失败"
        exit 1
    fi
    success "编译完成"
fi

# 创建 tmux 会话
log "创建 tmux 会话: $SESSION_NAME"
tmux new-session -d -s "$SESSION_NAME" -c "$PROJECT_ROOT"

# 设置会话环境
tmux send-keys -t "$SESSION_NAME" "cd '$PROJECT_ROOT'" Enter

# 创建自动重启脚本
cat > /tmp/arb_runner.sh << 'EOF'
#!/bin/bash

PROJECT_ROOT="$1"
ARB_BINARY="$PROJECT_ROOT/target/release/arb"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 设置高优先级（需要 root 权限）
if [ "$EUID" -eq 0 ]; then
    log "以 root 权限运行，设置高优先级调度..."
else
    warn "非 root 权限运行，无法设置高优先级调度"
fi

restart_count=0
max_restarts=10
restart_window=300  # 5分钟内最多重启次数

while true; do
    log "启动 arb 程序 (重启次数: $restart_count)"
    
    # 记录启动时间
    start_time=$(date +%s)
    
    # 运行 arb 程序
    "$ARB_BINARY"
    exit_code=$?
    
    # 记录结束时间
    end_time=$(date +%s)
    runtime=$((end_time - start_time))
    
    if [ $exit_code -eq 0 ]; then
        success "arb 程序正常退出"
        break
    else
        error "arb 程序异常退出，退出码: $exit_code，运行时间: ${runtime}s"
    fi
    
    # 检查是否运行时间太短（可能是配置错误）
    if [ $runtime -lt 10 ]; then
        warn "程序运行时间过短 (${runtime}s)，可能存在配置问题"
        restart_count=$((restart_count + 1))
        
        if [ $restart_count -ge $max_restarts ]; then
            error "重启次数过多 ($restart_count)，停止自动重启"
            error "请检查配置和日志，手动修复问题后重新启动"
            break
        fi
        
        log "等待 30 秒后重启..."
        sleep 30
    else
        # 运行时间较长，重置重启计数
        restart_count=0
        log "等待 5 秒后重启..."
        sleep 5
    fi
done

log "arb 监控程序退出"
EOF

chmod +x /tmp/arb_runner.sh

# 在 tmux 会话中运行自动重启脚本
tmux send-keys -t "$SESSION_NAME" "/tmp/arb_runner.sh '$PROJECT_ROOT'" Enter

success "tmux 会话 '$SESSION_NAME' 已创建并启动 arb 程序"
log "使用以下命令管理会话:"
echo "  附加到会话: tmux attach-session -t $SESSION_NAME"
echo "  分离会话: Ctrl+B, 然后按 D"
echo "  杀死会话: tmux kill-session -t $SESSION_NAME"
echo "  查看所有会话: tmux list-sessions"

# 询问是否立即附加到会话
read -p "是否立即附加到会话? (y/n): " attach_now
if [[ $attach_now =~ ^[Yy]$ ]]; then
    log "附加到会话..."
    tmux attach-session -t "$SESSION_NAME"
fi
