import asyncio
import time
from binance import AsyncClient, BinanceSocketManager
from binance.exceptions import BinanceAPIException
import pandas as pd
import numpy as np
from aiohttp import ClientConnectorError
from typing import List, Tuple

# === 配置区域 ===
API_KEY = "YOUR_API_KEY"        # 只读行情可不填
API_SECRET = "YOUR_API_SECRET"  # 只读行情可不填

QUOTE_ASSETS = {"USDT", "BUSD", "BTC", "ETH"}  # 只统计这些计价的交易对
INTERVAL = "15m"           # 15 分钟线
DAYS = 7                   # 最近 7 天
CANDLES_PER_DAY = int(24 * 60 / 15)
LIMIT = DAYS * CANDLES_PER_DAY  # 7 天 * 96 = 672，<=1000
CONCURRENCY = 10          # 并发抓取任务数
RETRY_LIMIT = 3           # 单个请求最多重试次数
RATE_LIMIT_DELAY = 0.2    # 每个请求间隔(秒)，防止被限速

# === 异步抓取单个交易对 K 线并计算波动率 ===
async def fetch_volatility(client: AsyncClient, symbol: str) -> Tuple[str, float]:
    for attempt in range(1, RETRY_LIMIT + 1):
        try:
            klines = await client.get_klines(
                symbol=symbol,
                interval=INTERVAL,
                limit=LIMIT
            )
            closes = np.array([float(k[4]) for k in klines])
            # 计算对数收益率
            log_ret = np.diff(np.log(closes))
            vol = float(np.std(log_ret, ddof=1))  # 样本标准差
            return symbol, vol
        except (BinanceAPIException, ClientConnectorError) as e:
            if attempt == RETRY_LIMIT:
                print(f"[ERROR] {symbol} 重试 {attempt} 次仍失败：{e}")
                return symbol, np.nan
            await asyncio.sleep(1 * attempt)
    return symbol, np.nan

# === 主流程 ===
async def main():
    client = await AsyncClient.create(API_KEY, API_SECRET)
    info = await client.get_exchange_info()
    # 筛选现货可交易交易对
    symbols = [
        s["symbol"]
        for s in info["symbols"]
        if s["status"] == "TRADING"
        and s["isSpotTradingAllowed"]
        and s["quoteAsset"] in QUOTE_ASSETS
    ]
    print(f"共找到 {len(symbols)} 个现货交易对，准备并发抓取波动率...")

    sem = asyncio.Semaphore(CONCURRENCY)
    async def sem_task(sym):
        async with sem:
            await asyncio.sleep(RATE_LIMIT_DELAY)
            return await fetch_volatility(client, sym)

    tasks = [asyncio.create_task(sem_task(sym)) for sym in symbols]
    results = await asyncio.gather(*tasks)

    await client.close_connection()

    # 转 DataFrame，筛掉失败项，排序取前 15
    df = pd.DataFrame(results, columns=["symbol", "volatility"]).dropna()
    df = df.sort_values("volatility", ascending=False).reset_index(drop=True)
    top15 = df.head(15)

    print("\n过去一周（15m 级别）波动率最高的 15 个币种：")
    print(top15.to_markdown(index=False, floatfmt=".6f"))

    # 如果需要，将结果保存到本地 CSV
    top15.to_csv("top15_spot_volatility.csv", index=False)
    print("\n已将结果保存到 top15_spot_volatility.csv")

if __name__ == "__main__":
    asyncio.run(main())
