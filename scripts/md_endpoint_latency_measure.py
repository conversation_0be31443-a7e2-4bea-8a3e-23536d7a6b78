#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测量 Binance WS 域名各 IP 的 TCP 握手延迟
只做 socket.connect((ip,9443))，记录时间差
"""

import socket
import time
import statistics

# 配置
PORT        = 9443
NUM_TRIES   = 50         # 每个 IP 做多少次尝试
TIMEOUT_SEC = 1          # connect 超时时间（秒）

def measure_tcp_latency(ip: str) -> list[float]:
    latencies = []
    for i in range(NUM_TRIES):
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(TIMEOUT_SEC)
        t0 = time.time() * 1000
        try:
            s.connect((ip, PORT))
            t1 = time.time() * 1000
            latencies.append(t1 - t0)
        except Exception as e:
            # 连接失败就跳过
            # print(f"[{ip}] 尝试{i+1}失败: {e}")
            pass
        finally:
            s.close()
    return latencies

def main():
    # 解析域名到 IP 列表
    DOMAIN = "stream.binance.com"
    addrs  = socket.getaddrinfo(DOMAIN, PORT, proto=socket.IPPROTO_TCP)
    ips    = sorted({info[4][0] for info in addrs})
    print("待测 IP 列表:", ips, "\n")

    for ip in ips:
        lat = measure_tcp_latency(ip)
        if not lat:
            print(f"{ip:>15} | 全部 {NUM_TRIES} 次连接失败")
            continue

        avg = statistics.mean(lat)
        med = statistics.median(lat)
        p95 = statistics.quantiles(lat, n=100)[94]
        print(f"{ip:>15} | 成功 {len(lat)}/{NUM_TRIES} 次 | avg={avg:.2f} ms | med={med:.2f} ms | p95={p95:.2f} ms")

if __name__ == "__main__":
    main()
