#!/usr/bin/env python3
"""
Fetch spot trading fee list via Ed25519-signed request.
Compatible with both Linux & macOS; Python ≥3.8.
"""

import json
import base64
import time
import urllib.parse
from pathlib import Path

import requests
from cryptography.hazmat.primitives.serialization import load_pem_private_key

# === 配置 ===
# API keys are now loaded from config.json
def load_config():
    """Load configuration from config.json"""
    try:
        with open("../config.json", "r") as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print("Error: config.json not found. Please create it based on config.json.example")
        exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON format in config.json")
        exit(1)

# Load configuration
config = load_config()
API_KEY          = config["api_key"]
PRIVATE_KEY      = config["private_key_pem"].encode()
BASE_URL         = "https://api.binance.com"      # 若连 Testnet 请替换域名
SYMBOL           = None        # 填 "BTCUSDT" 可只查单个现货对，None=查全部


def load_private_key():
    return load_pem_private_key(PRIVATE_KEY, password=None)


def build_payload(params: dict) -> str:
    """
    按原始顺序拼接 param=value&param=value…
    Binance 对 Ed25519 不要求字典序，维持插入顺即可。
    """
    return urllib.parse.urlencode(params, safe=":")


def sign(payload: str, key) -> str:
    """Return base64-encoded Ed25519 signature."""
    sig_bytes = key.sign(payload.encode("ascii"))
    return base64.b64encode(sig_bytes).decode()    # str 形式，方便放进 query


def main():
    prv_key = load_private_key()

    # --- 1. 准备参数 ---
    params = {"timestamp": int(time.time() * 1000)}
    if SYMBOL:
        params["symbol"] = SYMBOL.upper()

    # --- 2. 计算签名 ---
    payload = build_payload(params)
    params["signature"] = sign(payload, prv_key)

    # --- 3. 发送请求 ---
    headers = {"X-MBX-APIKEY": API_KEY}
    url     = f"{BASE_URL}/sapi/v1/asset/tradeFee"

    resp = requests.get(url, params=params, headers=headers, timeout=5)
    with open("spot_trading_fee.json", "w") as f:
        json.dump(resp.json(), f, indent = 2)


if __name__ == "__main__":
    main()
