from collections import Counter
import re

def extract_pairs_from_line(line):
    # 使用正则表达式提取形如 (XPEOPLEUSDT, Forward) 的内容
    return re.findall(r'\(([^,]+),\s*(Forward|Reverse)\)', line)

def analyze_pairs_frequency(log_path):
    pair_counter = Counter()
    
    with open(log_path, 'r') as f:
        for line in f:
            if 'ring' in line:
                pairs = extract_pairs_from_line(line)
                for pair, direction in pairs:
                    pair_counter[pair] += 1
    
    # 按出现次数降序排序
    sorted_pairs = pair_counter.most_common()
    
    print("出现次数排序后的加密货币交易对：")
    for pair, count in sorted_pairs:
        print(f"{pair}: {count}")

# 示例用法，替换为你的日志文件路径
analyze_pairs_frequency("../run.log")

