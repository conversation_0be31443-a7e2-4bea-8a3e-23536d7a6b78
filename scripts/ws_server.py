#!/usr/bin/env python
import argparse, asyncio, ssl, websockets
import logging
logging.basicConfig(level=logging.DEBUG)
logging.getLogger("websockets.server").setLevel(logging.DEBUG)

def build_ssl_context(cert, key):
    ctx = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
    ctx.load_cert_chain(certfile=cert, keyfile=key)
    return ctx

async def echo_handler(websocket, delay):
    async for message in websocket:
        await asyncio.sleep(delay)     # 延迟回显
        await websocket.send(message)

async def main():
    p = argparse.ArgumentParser(description="TLS WebSocket Echo Server")
    p.add_argument("--host", default="0.0.0.0")
    p.add_argument("--port", type=int, default=8443)
    p.add_argument("--cert", default="../../misc/certs/server.crt")
    p.add_argument("--key",  default="server.key")
    p.add_argument("--delay", type=float, default=0.0,
                   help="echo delay in seconds")
    args = p.parse_args()

    ssl_ctx = build_ssl_context(args.cert, args.key)
    print(f"▶ Serving wss://{args.host}:{args.port}  (delay={args.delay}s)")

    async with websockets.serve(
        lambda ws: echo_handler(ws, args.delay),
        host=args.host,
        port=args.port,
        ssl=ssl_ctx,
        max_size=None,   # 不限制消息大小，方便压测
    ):
        await asyncio.Future()          # run forever

if __name__ == "__main__":
    asyncio.run(main())
