import dns.resolver
import json

def get_all_a_and_aaaa(domain):
    ips = set()
    try:
        for rdata in dns.resolver.resolve(domain, 'A'):
            ips.add(rdata.to_text())
    except Exception:
        pass
    try:
        for rdata in dns.resolver.resolve(domain, 'AAAA'):
            ips.add(rdata.to_text())
    except Exception:
        pass
    return list(ips)

if __name__ == "__main__":
    all_ips = get_all_a_and_aaaa("ws-api.binance.com")
    all_ips2 = get_all_a_and_aaaa("stream.binance.com")
    all_ips3 = get_all_a_and_aaaa("stream-sbe.binance.com")
    ips = {
        "ws-api.binance.com": all_ips,
        "stream.binance.com": all_ips2,
        "stream-sbe.binance.com": all_ips3
    }

    with open("dns_results.json", "w") as f:
        json.dump(ips, f)
