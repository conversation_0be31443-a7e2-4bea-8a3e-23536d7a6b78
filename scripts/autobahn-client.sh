#!/usr/bin/env bash
set -euo pipefail
set -x
SOURCE_DIR=$(readlink -f "${BASH_SOURCE[0]}")
SOURCE_DIR=$(dirname "$SOURCE_DIR")
cd "${SOURCE_DIR}/.."

can_run_docker_without_sudo() {
    docker info > /dev/null 2>&1
}

if can_run_docker_without_sudo; then
    DOCKER_CMD="docker"
else
    DOCKER_CMD="sudo docker"
fi
echo $DOCKER_CMD

CONTAINER_NAME=fuzzingserver
function cleanup() {
    $DOCKER_CMD container stop "${CONTAINER_NAME}"
}
trap cleanup TERM EXIT

function test_diff() {
    if ! diff -q \
        <(jq -S 'del(."Tungstenite" | .. | .duration?)' 'scripts/autobahn/expected-results.json') \
        <(jq -S 'del(."Tungstenite" | .. | .duration?)' 'scripts/autobahn/client/index.json')
    then
        echo 'Difference in results, either this is a regression or' \
             'one should update autobahn/expected-results.json with the new results.'
        exit 64
    fi
}

$DOCKER_CMD run -d --rm \
    -v "${PWD}/scripts/autobahn:/autobahn" \
    -p 9001:9001 \
    --init \
    --name "${CONTAINER_NAME}" \
    crossbario/autobahn-testsuite \
    wstest -m fuzzingserver -s '/autobahn/fuzzingserver.json'

sleep 5
LOG_LEVEL=Info TOTAL_COUNT=-1 CURRENT_COUNT=1 RUST_BACKTRACE=full cargo run --example autobahn-client
test_diff
