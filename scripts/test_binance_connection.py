#!/usr/bin/env python3
"""
测试Binance API连接和配置
"""

import json
import sys
from binance.client import Client
from binance.exceptions import BinanceAPIException

def load_config(config_path: str = '../config.json') -> dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 配置文件 {config_path} 不存在")
        return None
    except json.JSONDecodeError:
        print(f"错误: 配置文件 {config_path} 格式错误")
        return None

def test_connection(config: dict) -> bool:
    """测试API连接"""
    try:
        # 检查必要的配置
        if 'api_key' not in config:
            print("错误: 配置文件中缺少 api_key")
            return False
        if 'api_secret' not in config:
            print("错误: 配置文件中缺少 api_secret")
            return False
        
        # 创建客户端
        client = Client(
            api_key=config['api_key'],
            api_secret=config['api_secret'],
            testnet=False
        )
        
        # 测试连接
        print("测试API连接...")
        server_time = client.get_server_time()
        print(f"✓ 服务器时间: {server_time}")
        
        # 测试账户信息
        print("测试账户信息...")
        account_info = client.get_account()
        print(f"✓ 账户类型: {account_info.get('accountType', 'Unknown')}")
        print(f"✓ 交易权限: {account_info.get('permissions', [])}")
        
        # 显示余额
        print("\n当前余额:")
        balances = account_info['balances']
        non_zero_balances = []
        
        for balance in balances:
            asset = balance['asset']
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked
            
            if total > 0:
                non_zero_balances.append({
                    'asset': asset,
                    'free': free,
                    'locked': locked,
                    'total': total
                })
        
        if non_zero_balances:
            print(f"发现 {len(non_zero_balances)} 个非零余额:")
            for balance in non_zero_balances:
                print(f"  {balance['asset']}: {balance['total']} (可用: {balance['free']}, 锁定: {balance['locked']})")
        else:
            print("没有发现非零余额")
        
        # 测试价格获取
        print("\n测试价格获取...")
        try:
            btc_price = client.get_symbol_ticker(symbol="BTCUSDT")
            print(f"✓ BTC价格: {btc_price['price']} USDT")
        except Exception as e:
            print(f"⚠ 获取BTC价格失败: {e}")
        
        print("\n✓ 所有测试通过！API配置正确。")
        return True
        
    except BinanceAPIException as e:
        print(f"❌ Binance API错误: {e}")
        if "Invalid API-key" in str(e):
            print("提示: 请检查API Key是否正确")
        elif "Signature for this request is not valid" in str(e):
            print("提示: 请检查API Secret是否正确")
        elif "IP address" in str(e):
            print("提示: 请检查IP白名单设置")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试Binance API连接')
    parser.add_argument('--config', default='../config.json', help='配置文件路径')
    
    args = parser.parse_args()
    
    print("Binance API连接测试")
    print("=" * 50)
    
    # 加载配置
    config = load_config(args.config)
    if not config:
        return 1
    
    # 测试连接
    if test_connection(config):
        print("\n🎉 测试成功！你可以使用 balance_portfolio.py 脚本了。")
        return 0
    else:
        print("\n❌ 测试失败！请检查配置后重试。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
