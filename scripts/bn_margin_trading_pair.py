import requests
import csv
def get_spot_symbols():
    url = "https://api.binance.com/api/v3/exchangeInfo"
    r = requests.get(url)
    data = r.json()
    result = {}
    for s in data['symbols']:
        if s['status'] == 'TRADING' and s['isMarginTradingAllowed']:
            price_tick = 0
            lot_size = 0
            min_order_qty = 0
            min_national = 0
            for filt in s['filters']:
                if filt['filterType'] == 'PRICE_FILTER':
                    price_tick = filt['tickSize']
                if filt['filterType'] == 'LOT_SIZE':
                    lot_size = filt['stepSize']
                    min_order_qty = filt['minQty']
                if filt['filterType'] == 'NOTIONAL':
                    min_national = filt['minNotional']
            result[s['symbol']] = (
                s['quoteAsset'],
                s['baseAsset'],
                price_tick,
                lot_size,
                min_order_qty,
                min_national
            )
    return result

def get_ticker_data():
    url = "https://api.binance.com/api/v3/ticker/24hr"
    r = requests.get(url)
    return r.json()

def get_usd_prices():
    """Fetch USD price for major quote assets using USDT/BUSD pairs"""
    url = "https://api.binance.com/api/v3/ticker/price"
    r = requests.get(url)
    prices = {item['symbol']: float(item['price']) for item in r.json()}

    # USDT/BUSD considered USD stablecoins
    price_map = {
        'USDT': 1.0,
        'BUSD': 1.0,
        'FDUSD': 1.0,
        'TUSD': 1.0,
        'USDC': 1.0,
    }

    # Get other quote asset to USDT (like BTCUSDT → BTC)
    for symbol, price in prices.items():
        if symbol.endswith('USDT'):
            asset = symbol.replace('USDT', '')
            price_map[asset] = price
        if symbol.endswith('BUSD'):
            asset = symbol.replace('BUSD', '')
            price_map[asset] = price
        if symbol.endswith('FDUSD'):
            asset = symbol.replace('FDUSD', '')
            price_map[asset] = price
        if symbol.endswith('TUSD'):
            asset = symbol.replace('TUSD', '')
            price_map[asset] = price
        if symbol.endswith('USDC'):
            asset = symbol.replace('USDC', '')
            price_map[asset] = price


    return price_map

def calculate_usd_market_values():
    symbols = get_spot_symbols()
    tickers = get_ticker_data()
    usd_rates = get_usd_prices()

    results = []
    for ticker in tickers:
        symbol = ticker['symbol']
        if symbol not in symbols:
            continue

        quote_asset, base_asset, price_tick, lot_size, min_order_qty, min_national = symbols[symbol]
        quote_volume = float(ticker['quoteVolume'])

        quote_to_usd = usd_rates.get(quote_asset)
        if not quote_to_usd:
            continue  # skip unknown assets

        usd_volume = quote_volume * quote_to_usd
        results.append((symbol, usd_volume, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national))

    results.sort(key=lambda x: x[1], reverse=True)
    return results

def write_to_csv(data, filename='bn_margin_trading_pair_with_vol_in_usd.csv'):
    with open(filename, mode='w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Rank', 'Symbol', 'USD Volume', 'Quote Asset', 'Quote Volume', 'Base Asset', 'Price Tick', 'Lot Size', 'Min Order Qty', 'Min Notional'])
        for i, (symbol, usd_value, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national) in enumerate(data, start=1):
            writer.writerow([i, symbol, f"{usd_value:.2f}", quote_asset, f"{quote_volume:.2f}", base_asset, price_tick, lot_size, min_order_qty, min_national])
    print(f"\n✅ Results written to '{filename}'")

def main():
    mv_list = calculate_usd_market_values()
    print(f"{'Rank':<5} {'Symbol':<12} {'USD Volume':>20} {'QuoteAsset':>12} {'QuoteVolume':>20} {'BaseAsset':>12} {'PriceTick':>12} {'LotSize':>12} {'MinOrderQty':>12} {'MinNotional':>12}")
    print("-" * 80)
    for i, (symbol, usd_value, quote_asset, quote_volume, base_asset, price_tick, lot_size, min_order_qty, min_national) in enumerate(mv_list[:200], start=1):
        print(f"{i:<5} {symbol:<12} {usd_value:>20,.2f} {quote_asset:>12} {quote_volume:>20,.2f} {base_asset:>12} {price_tick:>12} {lot_size:>12} {min_order_qty:>12} {min_national:>12}")
    write_to_csv(mv_list)

if __name__ == "__main__":
    main()
