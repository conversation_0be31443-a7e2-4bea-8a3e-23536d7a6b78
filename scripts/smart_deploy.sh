#!/bin/bash

# 智能部署脚本 - 整合自动化部署和 keep_arb_running.sh
# 该脚本结合了 IP 优化部署和进程管理功能

set -euo pipefail

# 配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"
KEEP_SCRIPT="$PROJECT_ROOT/keep_arb_running.sh"
DEPLOY_SCRIPT="$SCRIPTS_DIR/deploy_arb.py"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${PURPLE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

step() {
    echo -e "${CYAN}📋 步骤 $1:${NC} $2"
}

# 显示帮助信息
show_help() {
    echo "智能部署脚本 - 整合 IP 优化和进程管理"
    echo ""
    echo "用法: $0 [选项] [命令]"
    echo ""
    echo "命令:"
    echo "  deploy          - 执行 IP 优化部署"
    echo "  start           - 部署并启动 arb 进程"
    echo "  restart         - 重新部署并重启 arb 进程"
    echo "  stop            - 停止 arb 进程"
    echo "  status          - 显示进程状态"
    echo "  attach          - 连接到 tmux 会话"
    echo "  logs            - 查看实时日志"
    echo "  optimize        - 仅执行 IP 优化（不重启）"
    echo "  help            - 显示此帮助信息"
    echo ""
    echo "选项:"
    echo "  -f, --force     - 强制重新部署"
    echo "  -q, --quiet     - 静默模式"
    echo "  -v, --verbose   - 详细输出"
    echo "  --no-optimize   - 跳过 IP 优化"
    echo "  --dry-run       - 仅显示将要执行的操作"
    echo ""
    echo "示例:"
    echo "  $0 start        # 完整部署并启动"
    echo "  $0 restart      # 重新部署并重启"
    echo "  $0 optimize     # 仅优化 IP 配置"
    echo "  $0 status       # 查看状态"
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    # 检查 keep_arb_running.sh
    if [[ ! -f "$KEEP_SCRIPT" ]]; then
        error "keep_arb_running.sh 不存在: $KEEP_SCRIPT"
        exit 1
    fi
    
    # 检查部署脚本
    if [[ ! -f "$DEPLOY_SCRIPT" ]]; then
        error "deploy_arb.py 不存在: $DEPLOY_SCRIPT"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        error "python3 未安装"
        exit 1
    fi
    
    # 检查 tmux
    if ! command -v tmux &> /dev/null; then
        error "tmux 未安装"
        exit 1
    fi
    
    # 检查 Cargo
    if ! command -v cargo &> /dev/null; then
        error "cargo 未安装"
        exit 1
    fi
    
    success "依赖检查完成"
}

# 执行 IP 优化部署
run_ip_optimization() {
    local force_deploy=${1:-false}
    
    step "1" "执行 IP 优化部署"
    
    cd "$PROJECT_ROOT"
    
    if [[ "$force_deploy" == "true" ]]; then
        info "强制重新部署模式"
    fi
    
    # 运行 Python 部署脚本
    if python3 "$DEPLOY_SCRIPT" --project-root "$PROJECT_ROOT"; then
        success "IP 优化部署完成"
        return 0
    else
        error "IP 优化部署失败"
        return 1
    fi
}

# 获取当前 arb 状态
get_arb_status() {
    cd "$PROJECT_ROOT"
    if [[ -f "$KEEP_SCRIPT" ]]; then
        "$KEEP_SCRIPT" status 2>/dev/null || echo "stopped"
    else
        echo "unknown"
    fi
}

# 部署命令
cmd_deploy() {
    local force_deploy=${1:-false}
    
    log "开始智能部署..."
    
    check_dependencies
    
    if ! run_ip_optimization "$force_deploy"; then
        exit 1
    fi
    
    success "部署完成"
    info "使用 '$0 start' 启动程序"
}

# 启动命令
cmd_start() {
    local force_deploy=${1:-false}
    
    log "开始智能部署并启动..."
    
    check_dependencies
    
    # 检查当前状态
    local current_status
    current_status=$(get_arb_status)
    
    if [[ "$current_status" == *"运行中"* ]] && [[ "$force_deploy" != "true" ]]; then
        warn "arb 进程已在运行"
        info "使用 --force 强制重新部署"
        return 0
    fi
    
    # 执行 IP 优化
    step "1" "执行 IP 优化部署"
    if ! run_ip_optimization "$force_deploy"; then
        exit 1
    fi
    
    # 启动进程
    step "2" "启动 arb 进程"
    cd "$PROJECT_ROOT"
    if "$KEEP_SCRIPT" start; then
        success "arb 进程已启动"
        
        # 等待一下然后显示状态
        sleep 2
        "$KEEP_SCRIPT" status
    else
        error "启动 arb 进程失败"
        exit 1
    fi
}

# 重启命令
cmd_restart() {
    log "重新部署并重启 arb 进程..."
    
    check_dependencies
    
    # 停止当前进程
    step "1" "停止当前进程"
    cd "$PROJECT_ROOT"
    "$KEEP_SCRIPT" stop || true
    
    # 执行 IP 优化
    step "2" "执行 IP 优化部署"
    if ! run_ip_optimization "true"; then
        exit 1
    fi
    
    # 重新启动
    step "3" "重新启动 arb 进程"
    if "$KEEP_SCRIPT" start; then
        success "arb 进程已重启"
        
        # 显示状态
        sleep 2
        "$KEEP_SCRIPT" status
    else
        error "重启 arb 进程失败"
        exit 1
    fi
}

# 仅优化命令
cmd_optimize() {
    log "仅执行 IP 优化..."
    
    check_dependencies
    
    if ! run_ip_optimization "true"; then
        exit 1
    fi
    
    success "IP 优化完成"
    warn "需要重启 arb 进程以应用新配置"
    info "使用 '$0 restart' 重启进程"
}

# 代理其他命令到 keep_arb_running.sh
proxy_command() {
    local cmd="$1"
    
    if [[ ! -f "$KEEP_SCRIPT" ]]; then
        error "keep_arb_running.sh 不存在"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    "$KEEP_SCRIPT" "$cmd"
}

# 主函数
main() {
    local command="${1:-help}"
    local force_deploy=false
    local quiet=false
    local verbose=false
    local no_optimize=false
    local dry_run=false
    
    # 解析选项
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_deploy=true
                shift
                ;;
            -q|--quiet)
                quiet=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --no-optimize)
                no_optimize=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -h|--help|help)
                show_help
                exit 0
                ;;
            deploy|start|restart|stop|status|attach|logs|optimize)
                command="$1"
                shift
                ;;
            *)
                error "未知选项或命令: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置输出模式
    if [[ "$quiet" == "true" ]]; then
        exec 1>/dev/null
    fi
    
    if [[ "$dry_run" == "true" ]]; then
        info "干运行模式 - 仅显示将要执行的操作"
        echo "命令: $command"
        echo "强制部署: $force_deploy"
        echo "跳过优化: $no_optimize"
        exit 0
    fi
    
    # 执行命令
    case "$command" in
        deploy)
            cmd_deploy "$force_deploy"
            ;;
        start)
            cmd_start "$force_deploy"
            ;;
        restart)
            cmd_restart
            ;;
        optimize)
            cmd_optimize
            ;;
        stop|status|attach|logs)
            proxy_command "$command"
            ;;
        *)
            error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
