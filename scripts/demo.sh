#!/bin/bash

# 演示自动化部署系统
# 这个脚本展示了整个系统的工作流程

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[演示]${NC} $1"
}

step() {
    echo -e "${CYAN}📋 步骤 $1:${NC} $2"
}

success() {
    echo -e "${GREEN}✅${NC} $1"
}

info() {
    echo -e "${PURPLE}ℹ️${NC} $1"
}

wait_for_user() {
    echo -e "${YELLOW}⏸️  按 Enter 继续...${NC}"
    read
}

echo "🚀 ARB 自动化部署和管理系统演示"
echo "=" * 60
echo ""

log "这个演示将展示整合后的 ARB 管理系统"
log "包括 IP 优化、进程管理和监控功能"
echo ""

wait_for_user

# 步骤 1: 显示当前配置
step "1" "查看当前 arb.rs 中的 IP 配置"
echo ""

current_market_ip=$(grep -o 'IpAddr::V4(Ipv4Addr::from_str("[^"]*")' "$PROJECT_ROOT/src/bin/arb.rs" | head -1 | grep -o '[0-9.]*')
current_order_ip=$(grep -o 'IpAddr::V4(Ipv4Addr::from_str("[^"]*")' "$PROJECT_ROOT/src/bin/arb.rs" | tail -1 | grep -o '[0-9.]*')

info "当前市场数据 IP: $current_market_ip"
info "当前订单 IP: $current_order_ip"
echo ""

wait_for_user

# 步骤 2: DNS 解析
step "2" "运行 DNS 解析获取最新 IP 地址"
echo ""

cd "$SCRIPTS_DIR"
python3 dns_resolve.py

if [ -f "dns_results.json" ]; then
    market_ips=$(python3 -c "import json; data=json.load(open('dns_results.json')); print(len(data['stream.binance.com']))")
    order_ips=$(python3 -c "import json; data=json.load(open('dns_results.json')); print(len(data['ws-api.binance.com']))")

    success "DNS 解析完成"
    info "发现 stream.binance.com 的 $market_ips 个 IP 地址"
    info "发现 ws-api.binance.com 的 $order_ips 个 IP 地址"
else
    echo "❌ DNS 解析失败"
    exit 1
fi
echo ""

wait_for_user

# 步骤 3: 显示整合后的脚本系统
step "3" "展示整合后的脚本系统"
echo ""

info "核心管理脚本:"
echo "  📄 keep_arb_running.sh - 进程管理（已存在）"
echo "  📄 scripts/smart_deploy.sh - 智能部署"
echo "  📄 scripts/arb_config.sh - 配置管理"
echo "  📄 scripts/deploy_arb.py - IP 优化引擎"
echo ""

info "推荐使用方法:"
echo "  🚀 智能部署: ./scripts/smart_deploy.sh start"
echo "  🔧 仅优化IP: ./scripts/smart_deploy.sh optimize"
echo "  📊 查看配置: ./scripts/arb_config.sh show-config"
echo "  🏥 健康检查: ./scripts/arb_config.sh health-check"
echo "  🧪 测试功能: python3 scripts/test_deploy.py"
echo ""

wait_for_user

# 步骤 4: 运行测试
step "4" "运行功能测试"
echo ""

cd "$PROJECT_ROOT"
python3 scripts/test_deploy.py
echo ""

success "功能测试通过"
echo ""

wait_for_user

# 步骤 5: 显示延迟检测器信息
step "5" "延迟检测器说明"
echo ""

info "延迟检测器会:"
echo "  ⏱️  测试所有 IP 组合的延迟"
echo "  📊 生成详细的性能报告"
echo "  🏆 自动选择最优的 IP 组合"
echo "  📈 使用百分位数分析（p10, p20, p50, p90, p99）"
echo ""

info "注意: 实际运行延迟检测器需要:"
echo "  🌐 网络连接到 Binance API"
echo "  🔑 有效的 API 密钥"
echo "  ⏰ 几分钟的测试时间"
echo ""

wait_for_user

# 步骤 6: tmux 运行说明
step "6" "tmux 自动重启功能"
echo ""

info "tmux 脚本提供:"
echo "  🔄 程序异常退出时自动重启"
echo "  📊 运行时间和重启次数统计"
echo "  🛡️  防止频繁重启的保护机制"
echo "  📝 详细的日志输出"
echo ""

info "tmux 会话管理:"
echo "  📋 查看会话: tmux list-sessions"
echo "  🔗 附加会话: tmux attach-session -t arb_trading"
echo "  ⏸️  分离会话: Ctrl+B, 然后按 D"
echo "  🛑 停止会话: tmux kill-session -t arb_trading"
echo ""

wait_for_user

# 步骤 7: 完整流程总结
step "7" "完整自动化流程总结"
echo ""

info "自动化部署流程:"
echo "  1️⃣  🔍 DNS 解析 - 获取最新 IP 地址"
echo "  2️⃣  ⏱️  延迟测试 - 测试所有 IP 组合"
echo "  3️⃣  🏆 优化选择 - 找到最优 IP 组合"
echo "  4️⃣  📝 代码更新 - 自动更新 arb.rs"
echo "  5️⃣  🔨 重新编译 - 编译最新版本"
echo "  6️⃣  🏃 程序运行 - 在 tmux 中启动"
echo ""

success "演示完成！"
echo ""

info "现在你可以运行:"
echo "  ./scripts/auto_deploy_and_run.sh --help  # 查看帮助"
echo "  ./scripts/auto_deploy_and_run.sh         # 完整部署和运行"
echo ""

log "感谢观看演示！"
