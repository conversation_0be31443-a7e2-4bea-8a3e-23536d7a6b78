#!/usr/bin/env python3
"""
Binance现货持仓平衡脚本
自动将所有持仓平均分配，使每个币种的USDT价值大约相等
"""

import json
import time
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Tuple
from binance.client import Client
from binance.exceptions import BinanceAPIException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PortfolioBalancer:
    def __init__(self, config_path: str = '../config.json'):
        """初始化平衡器"""
        self.config = self._load_config(config_path)

        # 检查必要的配置
        if 'api_key' not in self.config:
            raise ValueError("配置文件中缺少 api_key")
        if 'api_secret' not in self.config:
            raise ValueError("配置文件中缺少 api_secret，现货交易需要API Secret")

        self.client = Client(
            api_key=self.config['api_key'],
            api_secret=self.config['api_secret'],
            testnet=False  # 设置为True使用测试网
        )

        # 最小交易金额（USDT）
        self.min_trade_amount = Decimal('1.0')
        # 价格容差（避免频繁小额交易）
        self.tolerance = Decimal('0.05')  # 5%

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_path} 格式错误")
            raise

    def get_account_balances(self) -> Dict[str, Decimal]:
        """获取账户余额（只返回非零余额）"""
        try:
            account_info = self.client.get_account()
            balances = {}

            for balance in account_info['balances']:
                asset = balance['asset']
                free = Decimal(balance['free'])
                locked = Decimal(balance['locked'])
                total = free + locked

                if total > 0:
                    balances[asset] = total
                    logger.info(f"{asset}: {total}")

            return balances
        except BinanceAPIException as e:
            logger.error(f"获取账户余额失败: {e}")
            raise

    def get_prices(self, symbols: List[str]) -> Dict[str, Decimal]:
        """获取价格信息"""
        try:
            prices = {}
            ticker_prices = self.client.get_all_tickers()

            for ticker in ticker_prices:
                symbol = ticker['symbol']
                if symbol in symbols:
                    prices[symbol] = Decimal(ticker['price'])

            return prices
        except BinanceAPIException as e:
            logger.error(f"获取价格信息失败: {e}")
            raise

    def calculate_usdt_values(self, balances: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算每个币种的USDT价值"""
        usdt_values = {}

        # USDT本身
        if 'USDT' in balances:
            usdt_values['USDT'] = balances['USDT']

        # 其他币种需要获取对USDT的价格
        symbols_needed = []
        for asset in balances:
            if asset != 'USDT':
                symbols_needed.append(f"{asset}USDT")

        if symbols_needed:
            prices = self.get_prices(symbols_needed)

            for asset, balance in balances.items():
                if asset == 'USDT':
                    continue

                symbol = f"{asset}USDT"
                if symbol in prices:
                    usdt_values[asset] = balance * prices[symbol]
                    logger.info(f"{asset}: {balance} * {prices[symbol]} = {usdt_values[asset]} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格")

        return usdt_values

    def calculate_target_allocation(self, usdt_values: Dict[str, Decimal]) -> Tuple[Decimal, Dict[str, Decimal]]:
        """计算目标分配"""
        total_value = sum(usdt_values.values())
        num_assets = len(usdt_values)
        target_value_per_asset = total_value / num_assets

        logger.info(f"总价值: {total_value} USDT")
        logger.info(f"币种数量: {num_assets}")
        logger.info(f"每个币种目标价值: {target_value_per_asset} USDT")

        target_allocation = {}
        for asset in usdt_values:
            target_allocation[asset] = target_value_per_asset

        return target_value_per_asset, target_allocation

    def calculate_trades(self, balances: Dict[str, Decimal], usdt_values: Dict[str, Decimal],
                        target_allocation: Dict[str, Decimal]) -> List[Dict]:
        """计算需要执行的交易"""
        trades = []

        # 获取当前价格
        symbols_needed = []
        for asset in balances:
            if asset != 'USDT':
                symbols_needed.append(f"{asset}USDT")

        prices = self.get_prices(symbols_needed) if symbols_needed else {}

        for asset in usdt_values:
            current_value = usdt_values[asset]
            target_value = target_allocation[asset]
            difference = target_value - current_value

            # 检查是否需要交易（超过容差）
            if abs(difference) > target_value * self.tolerance and abs(difference) > self.min_trade_amount:
                if asset == 'USDT':
                    # USDT需要买入其他币种
                    if difference < 0:  # 需要减少USDT
                        trades.append({
                            'action': 'sell_usdt_for_others',
                            'amount': abs(difference),
                            'asset': 'USDT'
                        })
                else:
                    # 其他币种
                    symbol = f"{asset}USDT"
                    if symbol in prices:
                        if difference > 0:  # 需要买入
                            trades.append({
                                'action': 'buy',
                                'symbol': symbol,
                                'usdt_amount': difference,
                                'asset': asset,
                                'price': prices[symbol]
                            })
                        else:  # 需要卖出
                            trades.append({
                                'action': 'sell',
                                'symbol': symbol,
                                'usdt_amount': abs(difference),
                                'asset': asset,
                                'price': prices[symbol]
                            })

        return trades

    def execute_trade(self, trade: Dict) -> bool:
        """执行单个交易"""
        try:
            if trade['action'] == 'buy':
                # 买入
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                # 获取交易对信息
                symbol_info = self.client.get_symbol_info(symbol)

                # 计算数量（考虑最小数量限制）
                quantity = usdt_amount / trade['price']

                # 应用数量精度
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        quantity = quantity.quantize(step_size, rounding=ROUND_DOWN)
                        break

                logger.info(f"买入 {symbol}: 数量 {quantity}, 预估价值 {usdt_amount} USDT")

                # 执行市价买单
                order = self.client.order_market_buy(
                    symbol=symbol,
                    quantity=str(quantity)
                )

                logger.info(f"买单成功: {order['orderId']}")
                return True

            elif trade['action'] == 'sell':
                # 卖出
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                # 获取交易对信息
                symbol_info = self.client.get_symbol_info(symbol)

                # 计算数量
                quantity = usdt_amount / trade['price']

                # 应用数量精度
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        quantity = quantity.quantize(step_size, rounding=ROUND_DOWN)
                        break

                logger.info(f"卖出 {symbol}: 数量 {quantity}, 预估价值 {usdt_amount} USDT")

                # 执行市价卖单
                order = self.client.order_market_sell(
                    symbol=symbol,
                    quantity=str(quantity)
                )

                logger.info(f"卖单成功: {order['orderId']}")
                return True

        except BinanceAPIException as e:
            logger.error(f"交易执行失败: {e}")
            return False
        except Exception as e:
            logger.error(f"交易执行出错: {e}")
            return False

    def balance_portfolio(self, dry_run: bool = True):
        """执行投资组合平衡"""
        logger.info("开始投资组合平衡...")

        # 1. 获取当前余额
        logger.info("获取账户余额...")
        balances = self.get_account_balances()

        if not balances:
            logger.warning("没有发现任何余额")
            return

        # 2. 计算USDT价值
        logger.info("计算USDT价值...")
        usdt_values = self.calculate_usdt_values(balances)

        # 3. 计算目标分配
        logger.info("计算目标分配...")
        target_value_per_asset, target_allocation = self.calculate_target_allocation(usdt_values)

        # 4. 计算需要的交易
        logger.info("计算需要的交易...")
        trades = self.calculate_trades(balances, usdt_values, target_allocation)

        if not trades:
            logger.info("投资组合已经平衡，无需交易")
            return

        # 5. 显示交易计划
        logger.info("交易计划:")
        for i, trade in enumerate(trades, 1):
            logger.info(f"{i}. {trade}")

        # 6. 执行交易
        if dry_run:
            logger.info("这是模拟运行，不会执行实际交易")
        else:
            logger.info("开始执行交易...")
            for i, trade in enumerate(trades, 1):
                logger.info(f"执行交易 {i}/{len(trades)}")
                success = self.execute_trade(trade)
                if success:
                    logger.info(f"交易 {i} 成功")
                    time.sleep(1)  # 避免API限制
                else:
                    logger.error(f"交易 {i} 失败")
                    break

        logger.info("投资组合平衡完成")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Binance现货持仓平衡工具')
    parser.add_argument('--config', default='../config.json', help='配置文件路径')
    parser.add_argument('--live', action='store_true', help='执行实际交易（默认为模拟运行）')

    args = parser.parse_args()

    try:
        balancer = PortfolioBalancer(args.config)
        balancer.balance_portfolio(dry_run=not args.live)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0

if __name__ == '__main__':
    exit(main())
