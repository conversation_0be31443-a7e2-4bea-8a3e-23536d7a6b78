#!/usr/bin/env python3
"""
自动化交易程序部署脚本

该脚本自动化以下流程：
1. 运行 DNS 解析获取最新的 IP 地址
2. 运行延迟检测器测试所有 IP 组合
3. 解析结果找到最优 IP 组合
4. 更新 arb.rs 中的 IP 地址
5. 重新编译并运行交易程序
"""

import json
import re
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Tuple


class LatencyResult:
    """延迟测试结果"""
    def __init__(self, market_data_ip: str, order_ip: str,
                 market_data_latency: float, order_latency: float):
        self.market_data_ip = market_data_ip
        self.order_ip = order_ip
        self.market_data_latency = market_data_latency
        self.order_latency = order_latency
        self.total_score = market_data_latency + order_latency * 2  # 订单延迟权重更高

    def __str__(self):
        return (f"Market Data IP: {self.market_data_ip}, Order IP: {self.order_ip}, "
                f"Market Latency: {self.market_data_latency:.2f}ms, "
                f"Order Latency: {self.order_latency:.2f}ms, "
                f"Total Score: {self.total_score:.2f}")


class ArbDeployer:
    """交易程序自动部署器"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.scripts_dir = self.project_root / "scripts"
        self.src_dir = self.project_root / "src"
        self.arb_rs_path = self.src_dir / "bin" / "arb.rs"

    def run_dns_resolve(self) -> Dict[str, List[str]]:
        """运行 DNS 解析获取 IP 地址"""
        print("🔍 正在解析 DNS 获取最新 IP 地址...")

        dns_script = self.scripts_dir / "dns_resolve.py"
        dns_results_file = self.scripts_dir / "dns_results.json"

        # 运行 DNS 解析脚本
        result = subprocess.run(
            [sys.executable, str(dns_script)],
            cwd=self.scripts_dir,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            raise RuntimeError(f"DNS 解析失败: {result.stderr}")

        # 读取结果
        with open(dns_results_file, 'r') as f:
            dns_results = json.load(f)

        print(f"✅ DNS 解析完成:")
        print(f"   stream.binance.com: {len(dns_results['stream.binance.com'])} IPs")
        print(f"   ws-api.binance.com: {len(dns_results['ws-api.binance.com'])} IPs")

        return dns_results

    def run_latency_detector(self) -> List[LatencyResult]:
        """运行延迟检测器并解析结果"""
        print("⏱️  正在运行延迟检测器...")

        # 编译延迟检测器
        print("   编译延迟检测器...")
        build_result = subprocess.run(
            ["cargo", "build", "--release", "--bin", "order_latency_detector"],
            cwd=self.project_root,
            capture_output=True,
            text=True
        )

        if build_result.returncode != 0:
            raise RuntimeError(f"编译延迟检测器失败: {build_result.stderr}")

        # 运行延迟检测器
        print("   运行延迟测试...")
        detector_result = subprocess.run(
            ["./target/release/order_latency_detector"],
            cwd=self.project_root,
            capture_output=True,
            text=True
        )

        if detector_result.returncode != 0:
            print(f"⚠️  延迟检测器运行出现问题: {detector_result.stderr}")
            print("继续解析可用的输出...")

        # 解析延迟测试结果
        return self._parse_latency_results(detector_result.stdout)

    def _parse_latency_results(self, output: str) -> List[LatencyResult]:
        """解析延迟测试输出"""
        results = []
        lines = output.split('\n')

        current_market_ip = None
        current_order_ip = None
        current_market_latency = None
        current_order_latency = None

        for line in lines:
            line = line.strip()

            # 解析测试的 IP
            if line.startswith("testing market data ip:"):
                current_market_ip = line.split(": ")[1]
            elif line.startswith("testing order ip:"):
                current_order_ip = line.split(": ")[1]

            # 解析市场数据延迟（使用 p50 中位数）
            elif "Market Data Latency" in line:
                continue  # 跳过标题行
            elif "p10:" in line and "p50:" in line and current_market_latency is None:
                # 解析百分位数行，提取 p50 作为代表值
                # 示例: "  p10: 45.23us, p20: 46.12us, p50: 47.89us, p90: 52.34us, p99: 58.76us"
                p50_match = re.search(r'p50: ([\d.]+)us', line)
                if p50_match:
                    current_market_latency = float(p50_match.group(1)) / 1000.0  # 转换为毫秒

            # 解析订单延迟（使用 p50 中位数）
            elif "Order Response Latency" in line:
                continue  # 跳过标题行
            elif "p10:" in line and "p50:" in line and current_order_latency is None:
                # 解析百分位数行，提取 p50 作为代表值
                p50_match = re.search(r'p50: ([\d.]+)us', line)
                if p50_match:
                    current_order_latency = float(p50_match.group(1)) / 1000.0  # 转换为毫秒

            # 当遇到分隔线时，表示一组测试结束
            elif "=====================================" in line:
                if (current_market_ip and current_order_ip and
                    current_market_latency is not None and current_order_latency is not None):

                    result = LatencyResult(
                        current_market_ip, current_order_ip,
                        current_market_latency, current_order_latency
                    )
                    results.append(result)
                    print(f"   📊 {result}")

                # 重置延迟值，但保留 IP（可能会有多组测试使用相同 IP）
                current_market_latency = None
                current_order_latency = None

        return results

    def find_best_ips(self, results: List[LatencyResult]) -> Tuple[str, str]:
        """找到最优的 IP 组合"""
        if not results:
            raise RuntimeError("没有找到有效的延迟测试结果")

        # 按总分排序（越小越好）
        best_result = min(results, key=lambda r: r.total_score)

        print(f"🏆 最优 IP 组合:")
        print(f"   {best_result}")

        return best_result.market_data_ip, best_result.order_ip

    def update_arb_rs(self, market_data_ip: str, order_ip: str) -> None:
        """更新 arb.rs 中的 IP 地址"""
        print(f"📝 更新 arb.rs 中的 IP 地址...")

        with open(self.arb_rs_path, 'r') as f:
            content = f.read()

        # 更新市场数据 IP
        market_data_section = re.search(
            r'(market_data_url\.socket_addr = Some\(SocketAddr::new\(\s*)'
            r'IpAddr::V4\(Ipv4Addr::from_str\("[\d.]+"\)\.unwrap\(\)\)',
            content
        )

        if market_data_section:
            content = re.sub(
                r'(market_data_url\.socket_addr = Some\(SocketAddr::new\(\s*)'
                r'IpAddr::V4\(Ipv4Addr::from_str\("[\d.]+"\)\.unwrap\(\)\)',
                f'\\1IpAddr::V4(Ipv4Addr::from_str("{market_data_ip}").unwrap())',
                content
            )
            print(f"   ✅ 更新市场数据 IP: {market_data_ip}")
        else:
            print("   ⚠️  未找到市场数据 IP 配置")

        # 更新订单 IP
        order_section = re.search(
            r'(order_url\.socket_addr = Some\(SocketAddr::new\(\s*)'
            r'IpAddr::V4\(Ipv4Addr::from_str\("[\d.]+"\)\.unwrap\(\)\)',
            content
        )

        if order_section:
            content = re.sub(
                r'(order_url\.socket_addr = Some\(SocketAddr::new\(\s*)'
                r'IpAddr::V4\(Ipv4Addr::from_str\("[\d.]+"\)\.unwrap\(\)\)',
                f'\\1IpAddr::V4(Ipv4Addr::from_str("{order_ip}").unwrap())',
                content
            )
            print(f"   ✅ 更新订单 IP: {order_ip}")
        else:
            print("   ⚠️  未找到订单 IP 配置")

        # 写回文件
        with open(self.arb_rs_path, 'w') as f:
            f.write(content)

    def build_arb(self) -> None:
        """编译交易程序"""
        print("🔨 编译交易程序...")

        build_result = subprocess.run(
            ["cargo", "build", "--release", "--bin", "arb"],
            cwd=self.project_root,
            capture_output=True,
            text=True
        )

        if build_result.returncode != 0:
            raise RuntimeError(f"编译交易程序失败: {build_result.stderr}")

        print("   ✅ 编译完成")

    def deploy(self, run_arb: bool = False, use_keep_script: bool = True) -> None:
        """执行完整的部署流程"""
        try:
            print("🚀 开始自动化部署流程...")
            print("=" * 50)

            # 1. DNS 解析
            self.run_dns_resolve()

            # 2. 延迟检测
            latency_results = self.run_latency_detector()

            # 3. 找到最优 IP
            best_market_ip, best_order_ip = self.find_best_ips(latency_results)

            # 4. 更新代码
            self.update_arb_rs(best_market_ip, best_order_ip)

            # 5. 编译
            self.build_arb()

            print("=" * 50)
            print("🎉 部署完成!")
            print(f"   最优市场数据 IP: {best_market_ip}")
            print(f"   最优订单 IP: {best_order_ip}")

            if run_arb:
                if use_keep_script and (self.project_root / "keep_arb_running.sh").exists():
                    print("🏃 使用 keep_arb_running.sh 启动交易程序...")
                    subprocess.run(["./keep_arb_running.sh", "restart"], cwd=self.project_root)
                else:
                    print("🏃 直接启动交易程序...")
                    subprocess.run(["./target/release/arb"], cwd=self.project_root)
            else:
                if (self.project_root / "keep_arb_running.sh").exists():
                    print("💡 使用 keep_arb_running.sh 管理程序:")
                    print("   ./keep_arb_running.sh start   # 启动")
                    print("   ./keep_arb_running.sh status  # 状态")
                    print("   ./keep_arb_running.sh attach  # 连接")
                else:
                    print("💡 运行交易程序: ./target/release/arb")

        except Exception as e:
            print(f"❌ 部署失败: {e}")
            sys.exit(1)


def main():
    import argparse

    parser = argparse.ArgumentParser(description="自动化交易程序部署")
    parser.add_argument("--run", action="store_true", help="部署完成后自动运行交易程序")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--no-keep-script", action="store_true", help="不使用 keep_arb_running.sh，直接运行程序")

    args = parser.parse_args()

    deployer = ArbDeployer(args.project_root)
    deployer.deploy(run_arb=args.run, use_keep_script=not args.no_keep_script)


if __name__ == "__main__":
    main()
