#!/usr/bin/env python3
"""
测试部署脚本的 IP 更新功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from deploy_arb import ArbDeployer, LatencyResult

def test_ip_update():
    """测试 IP 更新功能"""
    print("🧪 测试 IP 更新功能...")
    
    # 创建测试用的延迟结果
    test_results = [
        LatencyResult("*******", "*******", 50.0, 20.0),  # 总分: 90
        LatencyResult("*******", "*******", 45.0, 15.0),  # 总分: 75 (最优)
        LatencyResult("*******", "*******", 55.0, 25.0),  # 总分: 105
    ]
    
    deployer = ArbDeployer(".")
    
    # 测试找到最优 IP
    best_market_ip, best_order_ip = deployer.find_best_ips(test_results)
    print(f"✅ 最优 IP 组合: 市场数据 {best_market_ip}, 订单 {best_order_ip}")
    
    # 读取当前 arb.rs 内容
    with open(deployer.arb_rs_path, 'r') as f:
        original_content = f.read()
    
    print("📝 当前 arb.rs 中的 IP 配置:")
    import re
    
    # 查找当前的 IP
    market_match = re.search(
        r'market_data_url\.socket_addr = Some\(SocketAddr::new\(\s*'
        r'IpAddr::V4\(Ipv4Addr::from_str\("([\d.]+)"\)\.unwrap\(\)\)',
        original_content
    )
    
    order_match = re.search(
        r'order_url\.socket_addr = Some\(SocketAddr::new\(\s*'
        r'IpAddr::V4\(Ipv4Addr::from_str\("([\d.]+)"\)\.unwrap\(\)\)',
        original_content
    )
    
    if market_match:
        print(f"   当前市场数据 IP: {market_match.group(1)}")
    if order_match:
        print(f"   当前订单 IP: {order_match.group(1)}")
    
    # 测试更新（但不实际写入）
    print("🔄 模拟更新 IP...")
    print(f"   将更新为: 市场数据 {best_market_ip}, 订单 {best_order_ip}")
    
    # 实际测试更新功能（创建备份）
    backup_content = original_content
    try:
        deployer.update_arb_rs(best_market_ip, best_order_ip)
        
        # 验证更新结果
        with open(deployer.arb_rs_path, 'r') as f:
            updated_content = f.read()
        
        # 检查是否正确更新
        updated_market_match = re.search(
            r'market_data_url\.socket_addr = Some\(SocketAddr::new\(\s*'
            r'IpAddr::V4\(Ipv4Addr::from_str\("([\d.]+)"\)\.unwrap\(\)\)',
            updated_content
        )
        
        updated_order_match = re.search(
            r'order_url\.socket_addr = Some\(SocketAddr::new\(\s*'
            r'IpAddr::V4\(Ipv4Addr::from_str\("([\d.]+)"\)\.unwrap\(\)\)',
            updated_content
        )
        
        if updated_market_match and updated_market_match.group(1) == best_market_ip:
            print("✅ 市场数据 IP 更新成功")
        else:
            print("❌ 市场数据 IP 更新失败")
        
        if updated_order_match and updated_order_match.group(1) == best_order_ip:
            print("✅ 订单 IP 更新成功")
        else:
            print("❌ 订单 IP 更新失败")
        
    finally:
        # 恢复原始内容
        with open(deployer.arb_rs_path, 'w') as f:
            f.write(backup_content)
        print("🔄 已恢复原始配置")

def test_latency_parsing():
    """测试延迟结果解析"""
    print("\n🧪 测试延迟结果解析...")
    
    # 模拟 order_latency_detector 的输出
    sample_output = """
testing market data ip: *******
testing order ip: *******

=== Endpoint Performance Report ===
Market Data IP: *******
Order API IP: *******
Total Orders Sent: 1000

Market Data Latency (from event_time):
  p10: 45.23us, p20: 46.12us, p50: 47.89us, p90: 52.34us, p99: 58.76us

Order Response Latency:
  p10: 12.34us, p20: 13.45us, p50: 15.67us, p90: 18.90us, p99: 22.11us
=====================================

testing market data ip: *******
testing order ip: *******

=== Endpoint Performance Report ===
Market Data IP: *******
Order API IP: *******
Total Orders Sent: 1000

Market Data Latency (from event_time):
  p10: 40.11us, p20: 41.22us, p50: 42.33us, p90: 45.44us, p99: 48.55us

Order Response Latency:
  p10: 10.11us, p20: 11.22us, p50: 12.33us, p90: 14.44us, p99: 16.55us
=====================================
"""
    
    deployer = ArbDeployer(".")
    results = deployer._parse_latency_results(sample_output)
    
    print(f"✅ 解析到 {len(results)} 个结果:")
    for result in results:
        print(f"   {result}")
    
    if len(results) == 2:
        print("✅ 解析结果数量正确")
        
        # 验证第一个结果
        r1 = results[0]
        if (r1.market_data_ip == "*******" and r1.order_ip == "*******" and
            abs(r1.market_data_latency - 0.04789) < 0.001 and  # 47.89us -> 0.04789ms
            abs(r1.order_latency - 0.01567) < 0.001):  # 15.67us -> 0.01567ms
            print("✅ 第一个结果解析正确")
        else:
            print("❌ 第一个结果解析错误")
        
        # 验证第二个结果
        r2 = results[1]
        if (r2.market_data_ip == "*******" and r2.order_ip == "*******" and
            abs(r2.market_data_latency - 0.04233) < 0.001 and  # 42.33us -> 0.04233ms
            abs(r2.order_latency - 0.01233) < 0.001):  # 12.33us -> 0.01233ms
            print("✅ 第二个结果解析正确")
        else:
            print("❌ 第二个结果解析错误")
    else:
        print("❌ 解析结果数量错误")

if __name__ == "__main__":
    print("🚀 部署脚本测试")
    print("=" * 40)
    
    test_latency_parsing()
    test_ip_update()
    
    print("=" * 40)
    print("✅ 测试完成")
