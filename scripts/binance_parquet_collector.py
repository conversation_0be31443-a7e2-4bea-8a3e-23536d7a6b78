#!/usr/bin/env python3
"""
Binance数据收集器 - Parquet版本
增强版本，支持多种数据格式和更好的错误处理
"""

import json
import time
import threading
import socket
import ssl
import base64
import hashlib
import struct
import os
import sys
from datetime import datetime, timezone
from urllib.parse import urlparse
import gzip
import pandas as pd
import argparse
from pathlib import Path

# 导入SBE解析器
try:
    from sbe_parser import BinanceSbeParser
    SBE_PARSER_AVAILABLE = True
except ImportError:
    print("⚠️  警告: 无法导入SBE解析器，SBE功能将被禁用")
    SBE_PARSER_AVAILABLE = False
from sbe_parser import BinanceSbeParser

class WebSocketClient:
    def __init__(self, url, api_key=None):
        self.url = url
        self.api_key = api_key
        self.socket = None
        self.connected = False

        # 解析URL
        parsed = urlparse(url)
        self.host = parsed.hostname
        self.port = parsed.port or (443 if parsed.scheme == 'wss' else 80)
        self.path = parsed.path
        if parsed.query:
            self.path += '?' + parsed.query

        self.is_ssl = parsed.scheme == 'wss'

    def connect(self):
        """建立WebSocket连接"""
        try:
            # 创建socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)  # 增加超时时间到30秒

            if self.is_ssl:
                context = ssl.create_default_context()
                self.socket = context.wrap_socket(self.socket, server_hostname=self.host)

            # 连接到服务器
            self.socket.connect((self.host, self.port))

            # 发送WebSocket握手请求
            key = base64.b64encode(os.urandom(16)).decode()

            request = f"GET {self.path} HTTP/1.1\r\n"
            request += f"Host: {self.host}\r\n"
            request += "Upgrade: websocket\r\n"
            request += "Connection: Upgrade\r\n"
            request += f"Sec-WebSocket-Key: {key}\r\n"
            request += "Sec-WebSocket-Version: 13\r\n"

            # 添加API Key header (SBE需要)
            if self.api_key:
                request += f"X-MBX-APIKEY: {self.api_key}\r\n"

            request += "\r\n"

            self.socket.send(request.encode())

            # 接收握手响应
            response = self.socket.recv(4096).decode()
            if "101 Switching Protocols" in response:
                self.connected = True
                return True
            else:
                return False

        except Exception as e:
            print(f"连接失败: {e}")
            return False

    def recv_frame(self):
        """接收WebSocket帧"""
        if not self.connected:
            return None

        try:
            # 读取帧头
            header = self.socket.recv(2)
            if len(header) < 2:
                return None

            # 解析帧头
            fin = (header[0] & 0x80) != 0
            opcode = header[0] & 0x0f
            masked = (header[1] & 0x80) != 0
            payload_len = header[1] & 0x7f

            # 读取扩展长度
            if payload_len == 126:
                extended_len = struct.unpack('>H', self.socket.recv(2))[0]
                payload_len = extended_len
            elif payload_len == 127:
                extended_len = struct.unpack('>Q', self.socket.recv(8))[0]
                payload_len = extended_len

            # 读取掩码（如果有）
            if masked:
                mask = self.socket.recv(4)

            # 读取载荷数据
            payload = b''
            while len(payload) < payload_len:
                chunk = self.socket.recv(payload_len - len(payload))
                if not chunk:
                    break
                payload += chunk

            # 解除掩码（如果有）
            if masked:
                payload = bytes(payload[i] ^ mask[i % 4] for i in range(len(payload)))

            # 处理不同类型的帧
            if opcode == 0x1:  # 文本帧
                return payload.decode('utf-8')
            elif opcode == 0x2:  # 二进制帧
                return payload
            elif opcode == 0x8:  # 关闭帧
                self.connected = False
                return None
            elif opcode == 0x9:  # Ping帧
                self.send_pong(payload)
                return self.recv_frame()  # 继续接收下一帧
            elif opcode == 0xa:  # Pong帧
                return self.recv_frame()  # 继续接收下一帧
            else:
                return payload.decode('utf-8') if payload else None

        except Exception as e:
            print(f"接收数据错误: {e}")
            self.connected = False
            return None

    def send_pong(self, payload):
        """发送Pong帧"""
        try:
            frame = bytearray()
            frame.append(0x8a)  # FIN=1, opcode=0xa (pong)
            frame.append(len(payload))  # 载荷长度
            frame.extend(payload)
            self.socket.send(frame)
        except:
            pass

    def close(self):
        """关闭连接"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        self.connected = False


class BinanceParquetCollector:
    def __init__(self, symbols=["btcusdt"], data_dir="binance_data", save_format="parquet", use_sbe=True, api_key=None):
        self.symbols = [symbol.lower() for symbol in symbols] if isinstance(symbols, list) else [symbols.lower()]
        self.data_dir = Path(data_dir)
        self.save_format = save_format.lower()  # "parquet", "json", "both"
        self.use_sbe = use_sbe  # 是否使用SBE格式
        self.api_key = api_key  # SBE格式需要API Key

        # 为每个symbol创建独立的数据缓存
        self.bbo_data = {symbol: [] for symbol in self.symbols}
        self.trade_data = {symbol: [] for symbol in self.symbols}
        self.data_lock = threading.Lock()
        self.running = False

        # 创建数据目录
        self.data_dir.mkdir(exist_ok=True)

        # 统计信息
        self.stats = {
            'bbo_count': {symbol: 0 for symbol in self.symbols},
            'trade_count': {symbol: 0 for symbol in self.symbols},
            'start_time': None,
            'last_save_time': None,
            'save_count': 0
        }

        # 验证SBE配置
        if self.use_sbe and not self.api_key:
            print("⚠️  警告: SBE格式需要API Key，将自动切换到JSON格式")
            self.use_sbe = False

    def start_collection(self):
        """开始数据收集"""
        self.running = True
        self.stats['start_time'] = datetime.now(timezone.utc)

        # 为每个symbol启动数据收集线程
        for symbol in self.symbols:
            # 启动BBO数据收集线程
            bbo_thread = threading.Thread(target=self._collect_bbo_data, args=(symbol,), daemon=True)
            bbo_thread.start()

            # 启动逐笔成交数据收集线程
            trade_thread = threading.Thread(target=self._collect_trade_data, args=(symbol,), daemon=True)
            trade_thread.start()

        # 启动定时保存线程
        save_thread = threading.Thread(target=self._periodic_save, daemon=True)
        save_thread.start()

        # 启动状态监控线程
        monitor_thread = threading.Thread(target=self._monitor_status, daemon=True)
        monitor_thread.start()

        print(f"开始收集 {', '.join([s.upper() for s in self.symbols])} 的数据...")
        print(f"数据格式: {'SBE' if self.use_sbe else 'JSON'}")
        print(f"数据目录: {self.data_dir}")
        print(f"保存格式: {self.save_format}")

    def _collect_bbo_data(self, symbol):
        """收集BBO数据"""
        url = f"wss://stream-sbe.binance.com:9443/ws/{symbol}@bestBidAsk"
        while self.running:
            try:
                ws = WebSocketClient(url, api_key=self.api_key if self.use_sbe else None)
                if ws.connect():
                    print(f"{symbol.upper()} BBO WebSocket连接成功 ({'SBE' if self.use_sbe else 'JSON'})")

                    # SBE格式不需要发送订阅消息，直接连接到stream URL即可

                    while self.running and ws.connected:
                        data = ws.recv_frame()
                        if data:
                            try:
                                if self.use_sbe:
                                    # 处理SBE二进制数据
                                    bbo_record = self._parse_sbe_bbo_data(data, symbol)
                                else:
                                    # 处理JSON数据
                                    bbo = json.loads(data)
                                    timestamp = int(time.time() * 1000)

                                    bbo_record = {
                                        'timestamp': timestamp,
                                        'symbol': bbo.get('s'),
                                        'bid_price': float(bbo.get('b', 0)),
                                        'bid_qty': float(bbo.get('B', 0)),
                                        'ask_price': float(bbo.get('a', 0)),
                                        'ask_qty': float(bbo.get('A', 0)),
                                        'update_id': bbo.get('u')
                                    }

                                if bbo_record:
                                    with self.data_lock:
                                        self.bbo_data[symbol].append(bbo_record)
                                        self.stats['bbo_count'][symbol] += 1

                            except (json.JSONDecodeError, Exception) as e:
                                if not self.use_sbe:  # JSON解析错误才打印
                                    print(f"解析BBO数据错误: {e}")
                                continue

                ws.close()

            except Exception as e:
                print(f"{symbol.upper()} BBO连接错误: {e}")
                time.sleep(5)  # 重连延迟

    def _collect_trade_data(self, symbol):
        if self.use_sbe:
            url = f"wss://stream-sbe.binance.com:9443/ws/{symbol}@trade"
        else:
            url = f"wss://stream.binance.com:9443/ws/{symbol}@trade"

        while self.running:
            try:
                ws = WebSocketClient(url, api_key=self.api_key if self.use_sbe else None)
                if ws.connect():
                    print(f"{symbol.upper()} Trade WebSocket连接成功 ({'SBE' if self.use_sbe else 'JSON'})")

                    # SBE格式不需要发送订阅消息，直接连接到stream URL即可

                    while self.running and ws.connected:
                        data = ws.recv_frame()
                        if data:
                            try:
                                trade = json.loads(data)

                                trade_record = {
                                    'timestamp': trade.get('T'),
                                    'symbol': trade.get('s'),
                                    'trade_id': trade.get('t'),
                                    'price': float(trade.get('p', 0)),
                                    'quantity': float(trade.get('q', 0)),
                                    'buyer_order_id': trade.get('b'),
                                    'seller_order_id': trade.get('a'),
                                    'trade_time': trade.get('T'),
                                    'is_buyer_maker': trade.get('m')
                                }

                                if trade_record:
                                    with self.data_lock:
                                        self.trade_data[symbol].append(trade_record)
                                        self.stats['trade_count'][symbol] += 1

                            except (json.JSONDecodeError, Exception) as e:
                                if not self.use_sbe:  # JSON解析错误才打印
                                    print(f"解析Trade数据错误: {e}")
                                continue

                ws.close()

            except Exception as e:
                print(f"{symbol.upper()} Trade连接错误: {e}")
                time.sleep(5)  # 重连延迟

    def _parse_sbe_bbo_data(self, data, symbol):
        """解析SBE格式的BBO数据"""
        try:
            if not SBE_PARSER_AVAILABLE:
                print(f"SBE解析器不可用，跳过 {len(data)} 字节数据")
                return None

            if isinstance(data, str):
                # 如果收到的是JSON响应，跳过
                return None

            # 使用SBE解析器解析完整消息（包含头部）
            result = BinanceSbeParser.parse_sbe_message(data, debug=True)
            if result and result['type'] == 'bbo':
                book_ticker = result['data']
                return {
                    'timestamp': book_ticker.event_time // 1000,  # 微秒转毫秒
                    'symbol': book_ticker.symbol,
                    'bid_price': book_ticker.bid_price,
                    'bid_qty': book_ticker.bid_qty,
                    'ask_price': book_ticker.ask_price,
                    'ask_qty': book_ticker.ask_qty,
                    'update_id': book_ticker.book_update_id
                }
            else:
                # 尝试直接解析消息体（无头部）
                book_ticker = BinanceSbeParser.parse_best_bid_ask(data)
                if book_ticker:
                    return {
                        'timestamp': book_ticker.event_time // 1000,  # 微秒转毫秒
                        'symbol': book_ticker.symbol,
                        'bid_price': book_ticker.bid_price,
                        'bid_qty': book_ticker.bid_qty,
                        'ask_price': book_ticker.ask_price,
                        'ask_qty': book_ticker.ask_qty,
                        'update_id': book_ticker.book_update_id
                    }
                else:
                    print(f"无法解析SBE BBO数据 {len(data)} 字节")
                    # 调试真实数据结构
                    BinanceSbeParser.debug_sbe_data(data, "real_bbo_data")
                    return None

        except Exception as e:
            print(f"解析SBE BBO数据错误: {e}")
            return None

    def _parse_sbe_trade_data(self, data, symbol):
        """解析SBE格式的交易数据"""
        try:
            if not SBE_PARSER_AVAILABLE:
                print(f"SBE解析器不可用，跳过 {len(data)} 字节数据")
                return None

            if isinstance(data, str):
                # 如果收到的是JSON响应，跳过
                return None

            # 使用SBE解析器解析完整消息（包含头部）
            result = BinanceSbeParser.parse_sbe_message(data, debug=True)
            if result and result['type'] == 'trade':
                trade = result['data']
                return {
                    'timestamp': trade.event_time // 1000,  # 微秒转毫秒
                    'symbol': trade.symbol,
                    'trade_id': trade.trade_id,
                    'price': trade.price,
                    'quantity': trade.quantity,
                    'buyer_order_id': trade.buyer_order_id,
                    'seller_order_id': trade.seller_order_id,
                    'trade_time': trade.trade_time // 1000,  # 微秒转毫秒
                    'is_buyer_maker': trade.is_buyer_maker
                }
            else:
                # 尝试直接解析消息体（无头部）
                trade = BinanceSbeParser.parse_trade(data)
                if trade:
                    return {
                        'timestamp': trade.event_time // 1000,  # 微秒转毫秒
                        'symbol': trade.symbol,
                        'trade_id': trade.trade_id,
                        'price': trade.price,
                        'quantity': trade.quantity,
                        'buyer_order_id': trade.buyer_order_id,
                        'seller_order_id': trade.seller_order_id,
                        'trade_time': trade.trade_time // 1000,  # 微秒转毫秒
                        'is_buyer_maker': trade.is_buyer_maker
                    }
                else:
                    print(f"无法解析SBE Trade数据 {len(data)} 字节")
                    # 调试真实数据结构
                    BinanceSbeParser.debug_sbe_data(data, "real_trade_data")
                    return None

        except Exception as e:
            print(f"解析SBE Trade数据错误: {e}")
            return None

    def _periodic_save(self):
        """定期保存数据（每小时）"""
        while self.running:
            time.sleep(3600)  # 等待1小时
            self.save_data()

    def _monitor_status(self):
        """监控状态"""
        while self.running:
            time.sleep(60)  # 每分钟打印一次状态
            with self.data_lock:
                status_lines = []
                for symbol in self.symbols:
                    bbo_count = len(self.bbo_data[symbol])
                    trade_count = len(self.trade_data[symbol])
                    total_bbo = self.stats['bbo_count'][symbol]
                    total_trade = self.stats['trade_count'][symbol]

                    status_lines.append(f"{symbol.upper()}: BBO缓存 {bbo_count}, 交易缓存 {trade_count}, "
                                      f"总计 BBO {total_bbo}, 交易 {total_trade}")

                print("=" * 80)
                print("状态监控:")
                for line in status_lines:
                    print(f"  {line}")
                print("=" * 80)

    def save_data(self):
        """保存数据到文件"""
        now = datetime.now(timezone.utc)
        timestamp_str = now.strftime("%Y%m%d_%H%M%S")

        with self.data_lock:
            # 为每个symbol复制和清空数据
            bbo_data_copy = {}
            trade_data_copy = {}

            for symbol in self.symbols:
                bbo_data_copy[symbol] = self.bbo_data[symbol].copy()
                trade_data_copy[symbol] = self.trade_data[symbol].copy()
                self.bbo_data[symbol].clear()
                self.trade_data[symbol].clear()

        # 为每个symbol保存数据
        for symbol in self.symbols:
            # 保存BBO数据
            if bbo_data_copy[symbol]:
                self._save_bbo_data(bbo_data_copy[symbol], symbol, timestamp_str)

            # 保存交易数据
            if trade_data_copy[symbol]:
                self._save_trade_data(trade_data_copy[symbol], symbol, timestamp_str)

        self.stats['last_save_time'] = now
        self.stats['save_count'] += 1

    def _save_bbo_data(self, data, symbol, timestamp_str):
        """保存BBO数据"""
        if self.save_format in ["parquet", "both"]:
            # 保存为Parquet
            parquet_file = self.data_dir / f"{symbol}_bbo_{timestamp_str}.parquet"
            try:
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.to_parquet(parquet_file, compression='snappy', index=False)
                print(f"✅ 已保存 {len(data)} 条 {symbol.upper()} BBO数据到 {parquet_file}")
            except Exception as e:
                print(f"❌ 保存 {symbol.upper()} BBO Parquet失败: {e}")

        if self.save_format in ["json", "both"]:
            # 保存为JSON
            json_file = self.data_dir / f"{symbol}_bbo_{timestamp_str}.json"
            try:
                with open(json_file, 'w') as f:
                    json.dump(data, f, indent=2)
                print(f"✅ 已保存 {len(data)} 条 {symbol.upper()} BBO数据到 {json_file}")
            except Exception as e:
                print(f"❌ 保存 {symbol.upper()} BBO JSON失败: {e}")

    def _save_trade_data(self, data, symbol, timestamp_str):
        """保存交易数据"""
        if self.save_format in ["parquet", "both"]:
            # 保存为Parquet
            parquet_file = self.data_dir / f"{symbol}_trades_{timestamp_str}.parquet"
            try:
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df['trade_time'] = pd.to_datetime(df['trade_time'], unit='ms')
                df.to_parquet(parquet_file, compression='snappy', index=False)
                print(f"✅ 已保存 {len(data)} 条 {symbol.upper()} 交易数据到 {parquet_file}")
            except Exception as e:
                print(f"❌ 保存 {symbol.upper()} 交易 Parquet失败: {e}")

        if self.save_format in ["json", "both"]:
            # 保存为JSON
            json_file = self.data_dir / f"{symbol}_trades_{timestamp_str}.json"
            try:
                with open(json_file, 'w') as f:
                    json.dump(data, f, indent=2)
                print(f"✅ 已保存 {len(data)} 条 {symbol.upper()} 交易数据到 {json_file}")
            except Exception as e:
                print(f"❌ 保存 {symbol.upper()} 交易 JSON失败: {e}")

    def stop_collection(self):
        """停止数据收集"""
        print("正在停止数据收集...")
        self.running = False
        self.save_data()  # 保存剩余数据

        # 打印统计信息
        if self.stats['start_time']:
            duration = datetime.now(timezone.utc) - self.stats['start_time']
            print(f"运行时长: {duration}")

            for symbol in self.symbols:
                bbo_count = self.stats['bbo_count'][symbol]
                trade_count = self.stats['trade_count'][symbol]
                print(f"{symbol.upper()}: BBO {bbo_count} 条, 交易 {trade_count} 条")

            print(f"保存次数: {self.stats['save_count']}")

        print("数据收集已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Binance数据收集器 - 支持SBE和多symbol')
    parser.add_argument('--symbols', '-s', nargs='+', default=['btcusdt'],
                       help='交易对符号列表 (默认: btcusdt), 例如: --symbols btcusdt ethusdt adausdt')
    parser.add_argument('--data-dir', '-d', default='binance_data', help='数据保存目录 (默认: binance_data)')
    parser.add_argument('--format', '-f', choices=['parquet', 'json', 'both'],
                       default='parquet', help='保存格式 (默认: parquet)')
    parser.add_argument('--api-key', '-k', help='Binance API Key (SBE格式必需)')
    parser.add_argument('--sbe', action='store_true', default=True, help='使用SBE格式 (默认: 启用)')
    parser.add_argument('--json', action='store_true', help='使用JSON格式 (禁用SBE)')
    parser.add_argument('--test', action='store_true', help='测试模式，运行5分钟后自动停止')

    args = parser.parse_args()

    # 如果指定了--json，则禁用SBE
    use_sbe = args.sbe and not args.json

    # 检查SBE所需的API Key
    if use_sbe and not args.api_key:
        print("❌ 错误: SBE格式需要API Key")
        print("请使用 --api-key 参数提供您的Binance API Key")
        print("或使用 --json 参数切换到JSON格式")
        return

    print("Binance数据收集器 - 支持SBE和多symbol")
    print("=" * 60)
    print(f"交易对: {', '.join([s.upper() for s in args.symbols])}")
    print(f"数据格式: {'SBE' if use_sbe else 'JSON'}")
    if use_sbe:
        print(f"API Key: {args.api_key[:8]}...{args.api_key[-4:] if len(args.api_key) > 12 else '***'}")
    print(f"数据目录: {args.data_dir}")
    print(f"保存格式: {args.format}")
    print(f"测试模式: {'是' if args.test else '否'}")
    print("=" * 60)

    collector = BinanceParquetCollector(
        symbols=args.symbols,
        data_dir=args.data_dir,
        save_format=args.format,
        use_sbe=use_sbe,
        api_key=args.api_key
    )

    try:
        collector.start_collection()

        if args.test:
            print("测试模式：将在5分钟后自动停止")
            time.sleep(300)  # 5分钟
            collector.stop_collection()
        else:
            # 保持程序运行
            while True:
                time.sleep(1)

    except KeyboardInterrupt:
        print("\n收到停止信号...")
        collector.stop_collection()


if __name__ == "__main__":
    main()
