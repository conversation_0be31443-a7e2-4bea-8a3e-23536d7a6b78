#!/bin/bash

# 完整的自动化部署和运行脚本
# 该脚本执行以下步骤：
# 1. 运行 DNS 解析
# 2. 运行延迟检测
# 3. 更新 arb.rs 中的最优 IP
# 4. 编译程序
# 5. 在 tmux 中运行程序

set -e

# 配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${PURPLE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "自动化交易程序部署和运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -d, --deploy-only   仅执行部署，不运行程序"
    echo "  -r, --run-only      仅运行程序（跳过部署）"
    echo "  -f, --force         强制重新部署（即使已有最新版本）"
    echo "  --no-tmux          直接运行程序，不使用 tmux"
    echo ""
    echo "示例:"
    echo "  $0                  # 完整部署并运行"
    echo "  $0 -d               # 仅部署"
    echo "  $0 -r               # 仅运行"
    echo "  $0 --no-tmux        # 部署并直接运行（不使用 tmux）"
}

# 解析命令行参数
DEPLOY_ONLY=false
RUN_ONLY=false
FORCE_DEPLOY=false
USE_TMUX=true

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--deploy-only)
            DEPLOY_ONLY=true
            shift
            ;;
        -r|--run-only)
            RUN_ONLY=true
            shift
            ;;
        -f|--force)
            FORCE_DEPLOY=true
            shift
            ;;
        --no-tmux)
            USE_TMUX=false
            shift
            ;;
        *)
            error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        error "python3 未安装"
        exit 1
    fi
    
    # 检查 Cargo
    if ! command -v cargo &> /dev/null; then
        error "cargo 未安装"
        exit 1
    fi
    
    # 检查 tmux（如果需要）
    if [ "$USE_TMUX" = true ] && ! command -v tmux &> /dev/null; then
        error "tmux 未安装，请安装 tmux 或使用 --no-tmux 选项"
        exit 1
    fi
    
    # 检查 Python 依赖
    if ! python3 -c "import dns.resolver, json" 2>/dev/null; then
        warn "Python DNS 库未安装，正在安装..."
        pip3 install dnspython || {
            error "安装 dnspython 失败"
            exit 1
        }
    fi
    
    success "依赖检查完成"
}

# 执行部署
deploy() {
    log "开始自动化部署..."
    
    cd "$PROJECT_ROOT"
    
    # 运行 Python 部署脚本
    if ! python3 "$SCRIPTS_DIR/deploy_arb.py" --project-root "$PROJECT_ROOT"; then
        error "部署失败"
        exit 1
    fi
    
    success "部署完成"
}

# 运行程序
run_program() {
    log "启动交易程序..."
    
    cd "$PROJECT_ROOT"
    
    if [ "$USE_TMUX" = true ]; then
        # 使用 tmux 运行
        info "使用 tmux 运行程序..."
        bash "$SCRIPTS_DIR/run_arb_tmux.sh"
    else
        # 直接运行
        info "直接运行程序..."
        if [ ! -f "./target/release/arb" ]; then
            error "arb 二进制文件不存在，请先运行部署"
            exit 1
        fi
        ./target/release/arb
    fi
}

# 主逻辑
main() {
    echo "🚀 自动化交易程序部署和运行系统"
    echo "=" * 50
    
    # 检查依赖
    check_dependencies
    
    # 根据参数执行相应操作
    if [ "$RUN_ONLY" = true ]; then
        info "仅运行模式"
        run_program
    elif [ "$DEPLOY_ONLY" = true ]; then
        info "仅部署模式"
        deploy
    else
        info "完整部署和运行模式"
        deploy
        echo ""
        run_program
    fi
    
    echo "=" * 50
    success "操作完成"
}

# 错误处理
trap 'error "脚本执行失败，退出码: $?"' ERR

# 执行主函数
main "$@"
