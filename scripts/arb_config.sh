#!/bin/bash

# ARB 配置管理脚本
# 统一管理所有 ARB 相关的配置和功能

set -euo pipefail

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${PURPLE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "ARB 配置管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "配置命令:"
    echo "  show-config     - 显示当前配置"
    echo "  show-ips        - 显示当前 IP 配置"
    echo "  show-dns        - 显示 DNS 解析结果"
    echo "  backup-config   - 备份当前配置"
    echo "  restore-config  - 恢复配置"
    echo ""
    echo "管理命令:"
    echo "  full-deploy     - 完整部署（IP 优化 + 启动）"
    echo "  quick-start     - 快速启动（跳过 IP 优化）"
    echo "  health-check    - 健康检查"
    echo "  cleanup         - 清理日志和临时文件"
    echo ""
    echo "监控命令:"
    echo "  dashboard       - 显示监控面板"
    echo "  performance     - 显示性能统计"
    echo "  network-test    - 网络连接测试"
    echo ""
    echo "工具命令:"
    echo "  install-deps    - 安装依赖"
    echo "  setup           - 初始化设置"
    echo "  help            - 显示此帮助信息"
}

# 显示当前配置
show_config() {
    echo "🔧 当前 ARB 配置"
    echo "=" * 40
    
    # 项目信息
    echo "📁 项目路径: $PROJECT_ROOT"
    echo "📁 脚本路径: $SCRIPTS_DIR"
    
    # 检查关键文件
    echo ""
    echo "📄 关键文件状态:"
    local files=(
        "keep_arb_running.sh:进程管理脚本"
        "scripts/smart_deploy.sh:智能部署脚本"
        "scripts/deploy_arb.py:IP 优化脚本"
        "scripts/dns_resolve.py:DNS 解析脚本"
        "src/bin/arb.rs:主程序源码"
        "target/release/arb:编译后程序"
    )
    
    for file_info in "${files[@]}"; do
        IFS=':' read -r file desc <<< "$file_info"
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            echo -e "   ${GREEN}✓${NC} $desc ($file)"
        else
            echo -e "   ${RED}✗${NC} $desc ($file)"
        fi
    done
    
    # tmux 会话状态
    echo ""
    echo "🖥️  tmux 会话状态:"
    if command -v tmux &> /dev/null; then
        if tmux has-session -t "arb-session" 2>/dev/null; then
            echo -e "   ${GREEN}✓${NC} arb-session 会话存在"
        else
            echo -e "   ${YELLOW}○${NC} arb-session 会话不存在"
        fi
    else
        echo -e "   ${RED}✗${NC} tmux 未安装"
    fi
    
    # 进程状态
    echo ""
    echo "🔄 进程状态:"
    if [[ -f "$PROJECT_ROOT/keep_arb_running.sh" ]]; then
        cd "$PROJECT_ROOT"
        ./keep_arb_running.sh status 2>/dev/null || echo "   无法获取状态"
    else
        echo "   keep_arb_running.sh 不存在"
    fi
}

# 显示当前 IP 配置
show_ips() {
    echo "🌐 当前 IP 配置"
    echo "=" * 40
    
    local arb_file="$PROJECT_ROOT/src/bin/arb.rs"
    if [[ -f "$arb_file" ]]; then
        echo "📄 arb.rs 中的 IP 配置:"
        
        # 提取市场数据 IP
        local market_ip
        market_ip=$(grep -o 'IpAddr::V4(Ipv4Addr::from_str("[^"]*")' "$arb_file" | head -1 | grep -o '[0-9.]*' || echo "未找到")
        echo "   市场数据 IP: $market_ip"
        
        # 提取订单 IP
        local order_ip
        order_ip=$(grep -o 'IpAddr::V4(Ipv4Addr::from_str("[^"]*")' "$arb_file" | tail -1 | grep -o '[0-9.]*' || echo "未找到")
        echo "   订单 IP: $order_ip"
    else
        echo "❌ arb.rs 文件不存在"
    fi
    
    # 显示 DNS 解析结果
    echo ""
    echo "🔍 DNS 解析结果:"
    local dns_file="$SCRIPTS_DIR/dns_results.json"
    if [[ -f "$dns_file" ]]; then
        if command -v python3 &> /dev/null; then
            python3 -c "
import json
try:
    with open('$dns_file', 'r') as f:
        data = json.load(f)
    print(f'   stream.binance.com: {len(data.get(\"stream.binance.com\", []))} IPs')
    print(f'   ws-api.binance.com: {len(data.get(\"ws-api.binance.com\", []))} IPs')
    
    # 显示前几个 IP
    for domain, ips in data.items():
        print(f'   {domain} 示例:')
        for ip in ips[:3]:
            print(f'     - {ip}')
        if len(ips) > 3:
            print(f'     ... 还有 {len(ips) - 3} 个')
except Exception as e:
    print(f'   解析失败: {e}')
"
        else
            echo "   需要 Python3 来解析 JSON"
        fi
    else
        echo "   DNS 结果文件不存在，运行 'python3 scripts/dns_resolve.py' 生成"
    fi
}

# 完整部署
full_deploy() {
    echo "🚀 执行完整部署"
    echo "=" * 40
    
    if [[ -f "$SCRIPTS_DIR/smart_deploy.sh" ]]; then
        "$SCRIPTS_DIR/smart_deploy.sh" start --force
    else
        error "smart_deploy.sh 不存在"
        exit 1
    fi
}

# 快速启动
quick_start() {
    echo "⚡ 快速启动"
    echo "=" * 40
    
    if [[ -f "$PROJECT_ROOT/keep_arb_running.sh" ]]; then
        cd "$PROJECT_ROOT"
        ./keep_arb_running.sh start
    else
        error "keep_arb_running.sh 不存在"
        exit 1
    fi
}

# 健康检查
health_check() {
    echo "🏥 系统健康检查"
    echo "=" * 40
    
    local issues=0
    
    # 检查依赖
    echo "🔍 检查依赖:"
    local deps=("tmux" "cargo" "python3")
    for dep in "${deps[@]}"; do
        if command -v "$dep" &> /dev/null; then
            echo -e "   ${GREEN}✓${NC} $dep"
        else
            echo -e "   ${RED}✗${NC} $dep"
            ((issues++))
        fi
    done
    
    # 检查文件
    echo ""
    echo "📄 检查关键文件:"
    local files=(
        "keep_arb_running.sh"
        "scripts/smart_deploy.sh"
        "scripts/deploy_arb.py"
        "src/bin/arb.rs"
    )
    for file in "${files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            echo -e "   ${GREEN}✓${NC} $file"
        else
            echo -e "   ${RED}✗${NC} $file"
            ((issues++))
        fi
    done
    
    # 检查编译
    echo ""
    echo "🔨 检查编译状态:"
    cd "$PROJECT_ROOT"
    if cargo check --bin arb &>/dev/null; then
        echo -e "   ${GREEN}✓${NC} 编译检查通过"
    else
        echo -e "   ${RED}✗${NC} 编译检查失败"
        ((issues++))
    fi
    
    # 检查网络
    echo ""
    echo "🌐 检查网络连接:"
    if ping -c 1 ******* &>/dev/null; then
        echo -e "   ${GREEN}✓${NC} 网络连接正常"
    else
        echo -e "   ${RED}✗${NC} 网络连接异常"
        ((issues++))
    fi
    
    # 总结
    echo ""
    if [[ $issues -eq 0 ]]; then
        success "健康检查通过，系统状态良好"
    else
        warn "发现 $issues 个问题，请检查上述输出"
    fi
}

# 清理
cleanup() {
    echo "🧹 清理日志和临时文件"
    echo "=" * 40
    
    local cleaned=0
    
    # 清理日志
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        local log_count
        log_count=$(find "$PROJECT_ROOT/logs" -name "*.log" -mtime +7 | wc -l)
        if [[ $log_count -gt 0 ]]; then
            find "$PROJECT_ROOT/logs" -name "*.log" -mtime +7 -delete
            echo "   清理了 $log_count 个旧日志文件"
            ((cleaned++))
        fi
    fi
    
    # 清理临时文件
    local temp_files=("/tmp/arb_runner.sh")
    for temp_file in "${temp_files[@]}"; do
        if [[ -f "$temp_file" ]]; then
            rm -f "$temp_file"
            echo "   清理临时文件: $temp_file"
            ((cleaned++))
        fi
    done
    
    # 清理编译缓存
    if [[ -d "$PROJECT_ROOT/target" ]]; then
        echo "   清理编译缓存..."
        cd "$PROJECT_ROOT"
        cargo clean
        ((cleaned++))
    fi
    
    if [[ $cleaned -eq 0 ]]; then
        info "没有需要清理的文件"
    else
        success "清理完成"
    fi
}

# 安装依赖
install_deps() {
    echo "📦 安装依赖"
    echo "=" * 40
    
    # 检测操作系统
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        case $ID in
            ubuntu|debian)
                sudo apt-get update
                sudo apt-get install -y tmux python3 python3-pip
                pip3 install dnspython
                ;;
            centos|rhel|fedora)
                sudo yum install -y tmux python3 python3-pip
                pip3 install dnspython
                ;;
            *)
                warn "未知的操作系统，请手动安装 tmux, python3, dnspython"
                ;;
        esac
    else
        warn "无法检测操作系统，请手动安装依赖"
    fi
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        show-config)
            show_config
            ;;
        show-ips)
            show_ips
            ;;
        show-dns)
            show_ips  # DNS 信息包含在 show_ips 中
            ;;
        full-deploy)
            full_deploy
            ;;
        quick-start)
            quick_start
            ;;
        health-check)
            health_check
            ;;
        cleanup)
            cleanup
            ;;
        install-deps)
            install_deps
            ;;
        setup)
            install_deps
            health_check
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
