#!/usr/bin/env python3
"""fastest_ip_finder.py

Find the lowest‑latency IP behind a DNS record by combining **multi‑pass DNS enumeration** and
**TCP RTT measurement**.

It repeatedly queries the resolver to exhaust the rotating A‑record pool (Route 53/CloudFront
frequently returns only a subset per response), then measures each IP’s median TCP handshake
latency and prints a ranking plus the fastest candidate.

Usage::

    python fastest_ip_finder.py ws-api.binance.com            # default 443, 6 DNS passes
    python fastest_ip_finder.py ws-api.binance.com -p 9443    # change port
    python fastest_ip_finder.py ws-api.binance.com -q 12 -n 8 # more DNS passes / probes

Options:
    -p PORT,  --port PORT       TCP port to test (default 443)
    -n N,     --attempts N      Probes per IP (default 5)
    -t SEC,   --timeout SEC     Socket timeout in seconds (default 1.0)
    -q Q,     --queries Q       DNS queries to enumerate the pool (default 6)
    -r IP,    --resolver IP     Explicit resolver (e.g. *******). If omitted, system resolver.

Dependencies:
    • Python 3.8+
    • Either:
        – `dig` in PATH **or**
        – `dnspython` (pip install dnspython)

The script avoids ICMP‑based tools (which AWS/CloudFront often rate‑limit) by timing a full TCP
connect, which is closer to real application latency.
"""
from __future__ import annotations

import argparse
import random
import socket
import statistics
import subprocess
import sys
import time
from typing import Iterable, List, Set, Tuple


# ————————————————————————————————— DNS ——————————————————————————————————

def _dig_once(domain: str, resolver: str | None) -> List[str]:
    """Perform a single `dig +short` query and return A‑record strings."""
    cmd = ["dig", "+short", domain]
    if resolver:
        cmd.insert(1, f"@{resolver}")
    try:
        output = subprocess.check_output(cmd, text=True, timeout=2.0)
    except (FileNotFoundError, subprocess.SubprocessError):
        return []
    return [line.strip() for line in output.splitlines() if line.strip() and "." in line]


def _dnspython_once(domain: str, resolver: str | None) -> List[str]:
    """Single lookup via dnspython (fallback)."""
    try:
        import dns.resolver  # type: ignore
    except ImportError:
        return []
    try:
        res = dns.resolver.Resolver()
        if resolver:
            res.nameservers = [resolver]
        answer = res.resolve(domain, "A", lifetime=2.0)
        return [str(rdata) for rdata in answer]
    except Exception:
        return []


def resolve_ips(domain: str, *, resolver: str | None, queries: int) -> List[str]:
    """Enumerate (almost) all unique IPv4 addresses for *domain*.

    Because services like CloudFront rotate answers per query, we loop *queries* times
    with small random delays to maximise coverage.
    """
    ips: Set[str] = set()
    for _ in range(queries):
        # randomised sleep (0‑0.3 s) so we don’t bang the resolver with bursts and to
        # get a higher chance of distinct weighted‑RR responses.
        time.sleep(random.uniform(0, 0.3))
        new_ips = _dig_once(domain, resolver)
        if not new_ips:  # dig unavailable → dnspython fallback once per round
            new_ips = _dnspython_once(domain, resolver)
        ips.update(new_ips)
    if not ips:
        # final fallback: system resolver (may duplicate answers)
        try:
            infos = socket.getaddrinfo(domain, None, proto=socket.IPPROTO_TCP)
            ips.update(info[4][0] for info in infos)
        except socket.gaierror as e:
            sys.exit(f"DNS resolution failed: {e}")
    return sorted(ips)


# ———————————————————————————— TCP RTT —————————————————————————————

def probe_rtt(ip: str, port: int, timeout: float) -> float | None:
    """Measure RTT for a single TCP connect; return milliseconds or *None* on failure."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        sock.settimeout(timeout)
        start = time.perf_counter()
        try:
            sock.connect((ip, port))
        except (socket.timeout, OSError):
            return None
        return (time.perf_counter() - start) * 1000  # ms


def measure_ip(ip: str, *, port: int, attempts: int, timeout: float) -> Tuple[str, float | None]:
    samples: List[float] = []
    for _ in range(attempts):
        rtt = probe_rtt(ip, port, timeout)
        if rtt is not None:
            samples.append(rtt)
    median = statistics.median(samples) if samples else None
    return ip, median


# ————————————————————————————— CLI ——————————————————————————————

def main() -> None:
    p = argparse.ArgumentParser(description="Find the fastest IP behind a domain via TCP RTT.")
    p.add_argument("domain", help="Target domain name (e.g. ws-api.binance.com)")
    p.add_argument("-p", "--port", type=int, default=443, help="TCP port to test (default 443)")
    p.add_argument("-n", "--attempts", type=int, default=5, help="Probes per IP (default 5)")
    p.add_argument("-t", "--timeout", type=float, default=1.0, help="Connect timeout seconds (default 1.0)")
    p.add_argument("-q", "--queries", type=int, default=6, help="DNS queries to enumerate pool (default 6)")
    p.add_argument("-r", "--resolver", metavar="IP", help="Optional explicit DNS resolver, e.g. *******")

    args = p.parse_args()

    ips = resolve_ips(args.domain, resolver=args.resolver, queries=args.queries)
    if not ips:
        sys.exit("No A records found.")

    print(f"\nDiscovered {len(ips)} unique IPs for {args.domain} (after {args.queries} DNS passes):")
    print("  " + ", ".join(ips) + "\n")

    print("Measuring TCP connect RTT …\n")
    results: List[Tuple[str, float | None]] = [
        measure_ip(ip, port=args.port, attempts=args.attempts, timeout=args.timeout) for ip in ips
    ]

    valid = [(ip, rtt) for ip, rtt in results if rtt is not None]
    if not valid:
        sys.exit("All probes timed out or failed; check connectivity and port.")

    fastest_ip, fastest_rtt = min(valid, key=lambda x: x[1])

    # ——— Output ———
    print("Full ranking (median over attempts):")
    for ip, rtt in sorted(valid, key=lambda x: x[1]):
        print(f"  {ip:15}  {rtt:.2f} ms")

    print("\nFastest IP →")
    print(f"  {fastest_ip}  (median {fastest_rtt:.2f} ms across {args.attempts} probes)\n")

    # helpful exit code: 0 + print fastest, or 1 on error / all timed out
    # This allows piping:  FASTEST=$(python script.py domain | tail -1 | awk '{print $1}')


if __name__ == "__main__":
    main()

