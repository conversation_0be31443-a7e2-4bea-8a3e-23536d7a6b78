#!/usr/bin/env python3
"""
Binance BBO and Trade Data Collector
订阅Binance的BBO（最佳买卖价）和逐笔成交数据，每小时保存一次
"""

import json
import time
import threading
import socket
import ssl
import base64
import hashlib
import struct
import os
from datetime import datetime, timezone
from urllib.parse import urlparse
import gzip


class WebSocketClient:
    def __init__(self, url):
        self.url = url
        self.socket = None
        self.connected = False
        
    def connect(self):
        """建立WebSocket连接"""
        parsed = urlparse(self.url)
        host = parsed.hostname
        port = parsed.port or (443 if parsed.scheme == 'wss' else 80)
        
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        
        if parsed.scheme == 'wss':
            context = ssl.create_default_context()
            self.socket = context.wrap_socket(sock, server_hostname=host)
        else:
            self.socket = sock
            
        self.socket.connect((host, port))
        
        # WebSocket握手
        key = base64.b64encode(os.urandom(16)).decode()
        path = parsed.path or '/'
        if parsed.query:
            path += '?' + parsed.query
            
        handshake = (
            f"GET {path} HTTP/1.1\r\n"
            f"Host: {host}\r\n"
            f"Upgrade: websocket\r\n"
            f"Connection: Upgrade\r\n"
            f"Sec-WebSocket-Key: {key}\r\n"
            f"Sec-WebSocket-Version: 13\r\n"
            f"\r\n"
        )
        
        self.socket.send(handshake.encode())
        response = self.socket.recv(4096).decode()
        
        if "101 Switching Protocols" in response:
            self.connected = True
            print("WebSocket连接成功")
        else:
            raise Exception(f"WebSocket握手失败: {response}")
    
    def send_frame(self, data):
        """发送WebSocket帧"""
        if not self.connected:
            return
            
        payload = data.encode() if isinstance(data, str) else data
        payload_len = len(payload)
        
        # 构建帧头
        frame = bytearray()
        frame.append(0x81)  # FIN=1, opcode=1 (text)
        
        if payload_len < 126:
            frame.append(0x80 | payload_len)  # MASK=1
        elif payload_len < 65536:
            frame.append(0x80 | 126)
            frame.extend(struct.pack('>H', payload_len))
        else:
            frame.append(0x80 | 127)
            frame.extend(struct.pack('>Q', payload_len))
        
        # 添加掩码
        mask = os.urandom(4)
        frame.extend(mask)
        
        # 掩码处理payload
        masked_payload = bytearray()
        for i, byte in enumerate(payload):
            masked_payload.append(byte ^ mask[i % 4])
        
        frame.extend(masked_payload)
        self.socket.send(frame)
    
    def recv_frame(self):
        """接收WebSocket帧"""
        if not self.connected:
            return None
            
        try:
            # 读取帧头
            header = self.socket.recv(2)
            if len(header) < 2:
                return None
                
            fin = (header[0] & 0x80) != 0
            opcode = header[0] & 0x0f
            masked = (header[1] & 0x80) != 0
            payload_len = header[1] & 0x7f
            
            # 处理扩展长度
            if payload_len == 126:
                extended_len = self.socket.recv(2)
                payload_len = struct.unpack('>H', extended_len)[0]
            elif payload_len == 127:
                extended_len = self.socket.recv(8)
                payload_len = struct.unpack('>Q', extended_len)[0]
            
            # 读取掩码（如果有）
            if masked:
                mask = self.socket.recv(4)
            
            # 读取payload
            payload = b''
            while len(payload) < payload_len:
                chunk = self.socket.recv(payload_len - len(payload))
                if not chunk:
                    break
                payload += chunk
            
            # 解掩码
            if masked:
                unmasked = bytearray()
                for i, byte in enumerate(payload):
                    unmasked.append(byte ^ mask[i % 4])
                payload = bytes(unmasked)
            
            if opcode == 1:  # 文本帧
                return payload.decode()
            elif opcode == 2:  # 二进制帧
                return payload
            elif opcode == 8:  # 关闭帧
                self.connected = False
                return None
                
        except Exception as e:
            print(f"接收帧错误: {e}")
            self.connected = False
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.connected = False
            self.socket.close()


class BinanceDataCollector:
    def __init__(self, symbol="btcusdt"):
        self.symbol = symbol.lower()
        self.bbo_data = []
        self.trade_data = []
        self.data_lock = threading.Lock()
        self.running = False
        
        # 创建数据目录
        self.data_dir = "binance_data"
        os.makedirs(self.data_dir, exist_ok=True)
        
    def start_collection(self):
        """开始数据收集"""
        self.running = True
        
        # 启动BBO数据收集线程
        bbo_thread = threading.Thread(target=self._collect_bbo_data)
        bbo_thread.daemon = True
        bbo_thread.start()
        
        # 启动逐笔成交数据收集线程
        trade_thread = threading.Thread(target=self._collect_trade_data)
        trade_thread.daemon = True
        trade_thread.start()
        
        # 启动定时保存线程
        save_thread = threading.Thread(target=self._periodic_save)
        save_thread.daemon = True
        save_thread.start()
        
        print(f"开始收集 {self.symbol.upper()} 的BBO和逐笔成交数据...")
        
    def _collect_bbo_data(self):
        """收集BBO数据"""
        url = f"wss://stream.binance.com:9443/ws/{self.symbol}@bookTicker"
        
        while self.running:
            try:
                ws = WebSocketClient(url)
                ws.connect()
                
                while self.running and ws.connected:
                    data = ws.recv_frame()
                    if data:
                        try:
                            bbo = json.loads(data)
                            timestamp = int(time.time() * 1000)
                            
                            bbo_record = {
                                'timestamp': timestamp,
                                'symbol': bbo.get('s'),
                                'bid_price': float(bbo.get('b', 0)),
                                'bid_qty': float(bbo.get('B', 0)),
                                'ask_price': float(bbo.get('a', 0)),
                                'ask_qty': float(bbo.get('A', 0)),
                                'update_id': bbo.get('u')
                            }
                            
                            with self.data_lock:
                                self.bbo_data.append(bbo_record)
                                
                        except json.JSONDecodeError:
                            continue
                            
                ws.close()
                
            except Exception as e:
                print(f"BBO连接错误: {e}")
                time.sleep(5)  # 重连延迟
                
    def _collect_trade_data(self):
        """收集逐笔成交数据"""
        url = f"wss://stream.binance.com:9443/ws/{self.symbol}@trade"
        
        while self.running:
            try:
                ws = WebSocketClient(url)
                ws.connect()
                
                while self.running and ws.connected:
                    data = ws.recv_frame()
                    if data:
                        try:
                            trade = json.loads(data)
                            
                            trade_record = {
                                'timestamp': trade.get('T'),
                                'symbol': trade.get('s'),
                                'trade_id': trade.get('t'),
                                'price': float(trade.get('p', 0)),
                                'quantity': float(trade.get('q', 0)),
                                'buyer_order_id': trade.get('b'),
                                'seller_order_id': trade.get('a'),
                                'trade_time': trade.get('T'),
                                'is_buyer_maker': trade.get('m')
                            }
                            
                            with self.data_lock:
                                self.trade_data.append(trade_record)
                                
                        except json.JSONDecodeError:
                            continue
                            
                ws.close()
                
            except Exception as e:
                print(f"Trade连接错误: {e}")
                time.sleep(5)  # 重连延迟
                
    def _periodic_save(self):
        """定期保存数据（每小时）"""
        while self.running:
            time.sleep(3600)  # 等待1小时
            self.save_data()
            
    def save_data(self):
        """保存数据到文件"""
        now = datetime.now(timezone.utc)
        timestamp_str = now.strftime("%Y%m%d_%H%M%S")
        
        with self.data_lock:
            # 保存BBO数据
            if self.bbo_data:
                bbo_filename = f"{self.data_dir}/{self.symbol}_bbo_{timestamp_str}.json"
                with open(bbo_filename, 'w') as f:
                    json.dump(self.bbo_data, f, indent=2)
                print(f"已保存 {len(self.bbo_data)} 条BBO数据到 {bbo_filename}")
                self.bbo_data.clear()
            
            # 保存逐笔成交数据
            if self.trade_data:
                trade_filename = f"{self.data_dir}/{self.symbol}_trades_{timestamp_str}.json"
                with open(trade_filename, 'w') as f:
                    json.dump(self.trade_data, f, indent=2)
                print(f"已保存 {len(self.trade_data)} 条交易数据到 {trade_filename}")
                self.trade_data.clear()
                
    def stop_collection(self):
        """停止数据收集"""
        self.running = False
        self.save_data()  # 保存剩余数据
        print("数据收集已停止")


def main():
    # 可以修改交易对
    symbol = "btcusdt"  # 默认BTC/USDT
    
    collector = BinanceDataCollector(symbol)
    
    try:
        collector.start_collection()
        
        # 保持程序运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        collector.stop_collection()


if __name__ == "__main__":
    main()
