# Binance现货持仓平衡工具

这个Python脚本可以自动平衡你的Binance现货持仓，让每个币种的USDT价值大约相等。

## 功能特点

- 自动获取所有现货余额
- 计算每个币种的USDT价值
- 平均分配总价值到所有币种
- 执行必要的买卖交易来平衡持仓
- 支持模拟运行和实际交易
- 智能避免小额交易（设置最小交易金额和容差）

## 安装依赖

```bash
cd scripts
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp ../config.json.example ../config.json
```

2. 编辑 `../config.json` 文件，添加你的Binance API信息：
```json
{
  "api_key": "你的_BINANCE_API_KEY",
  "api_secret": "你的_BINANCE_API_SECRET"
}
```

**重要提醒：**
- 确保API密钥有现货交易权限
- 建议先在测试网测试
- 请妥善保管API密钥，不要泄露

## 使用方法

### 1. 模拟运行（推荐先使用）

```bash
python balance_portfolio.py
```

这会显示当前持仓情况和计划执行的交易，但不会实际执行。

### 2. 实际交易

```bash
python balance_portfolio.py --live
```

**警告：这会执行实际的买卖交易！**

### 3. 指定配置文件

```bash
python balance_portfolio.py --config /path/to/your/config.json
```

## 工作原理

1. **获取余额**：获取所有非零现货余额
2. **计算价值**：将所有币种转换为USDT价值
3. **计算目标**：总价值平均分配到所有币种
4. **生成交易**：计算需要买入/卖出的数量
5. **执行交易**：使用市价单执行交易

## 安全特性

- **容差设置**：只有偏差超过5%才会交易，避免频繁小额交易
- **最小金额**：只有超过1 USDT的差异才会交易
- **模拟模式**：默认为模拟运行，需要明确指定才会实际交易
- **错误处理**：完善的错误处理和日志记录

## 示例输出

```
2024-01-01 10:00:00 - INFO - 开始投资组合平衡...
2024-01-01 10:00:01 - INFO - 获取账户余额...
2024-01-01 10:00:01 - INFO - BTC: 0.001
2024-01-01 10:00:01 - INFO - ETH: 0.01
2024-01-01 10:00:01 - INFO - USDT: 50.0
2024-01-01 10:00:02 - INFO - 计算USDT价值...
2024-01-01 10:00:02 - INFO - BTC: 0.001 * 45000 = 45.0 USDT
2024-01-01 10:00:02 - INFO - ETH: 0.01 * 3000 = 30.0 USDT
2024-01-01 10:00:02 - INFO - 总价值: 125.0 USDT
2024-01-01 10:00:02 - INFO - 币种数量: 3
2024-01-01 10:00:02 - INFO - 每个币种目标价值: 41.67 USDT
2024-01-01 10:00:03 - INFO - 交易计划:
2024-01-01 10:00:03 - INFO - 1. {'action': 'sell', 'symbol': 'BTCUSDT', 'usdt_amount': 3.33, 'asset': 'BTC', 'price': 45000}
2024-01-01 10:00:03 - INFO - 2. {'action': 'buy', 'symbol': 'ETHUSDT', 'usdt_amount': 11.67, 'asset': 'ETH', 'price': 3000}
2024-01-01 10:00:03 - INFO - 这是模拟运行，不会执行实际交易
```

## 注意事项

1. **风险提醒**：
   - 这是自动交易工具，请谨慎使用
   - 建议先用小额资金测试
   - 市场波动可能影响平衡效果

2. **API限制**：
   - 注意Binance API的调用频率限制
   - 脚本已内置延迟来避免限制

3. **交易费用**：
   - 每次交易都会产生手续费
   - 频繁平衡可能导致费用累积

4. **最小交易量**：
   - 某些交易对有最小交易量限制
   - 脚本会自动处理这些限制

## 故障排除

1. **API错误**：检查API密钥和权限设置
2. **网络错误**：检查网络连接
3. **余额不足**：确保有足够余额进行交易
4. **交易对不存在**：某些币种可能没有对USDT的交易对

## 自定义设置

你可以在脚本中修改以下参数：

```python
# 最小交易金额（USDT）
self.min_trade_amount = Decimal('1.0')

# 价格容差（避免频繁小额交易）
self.tolerance = Decimal('0.05')  # 5%
```

## 支持

如果遇到问题，请检查：
1. API密钥配置是否正确
2. 网络连接是否正常
3. 账户余额是否充足
4. 日志输出中的错误信息
